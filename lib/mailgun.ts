import Mailgun from 'mailgun.js';
import {
  loadEmailTemplate,
  processHtmlTemplate,
} from '../util/load-email-template';
import { ISurveyDoc } from './models/survey';

const mailgun = new Mailgun(FormData);
const mailgunClient = mailgun.client({
  username: 'api',
  key: process.env.MAILGUN_API_KEY!,
});

export default async function sendSimpleMessages(
  surveyDocs: ISurveyDoc[]
): Promise<undefined> {
  const htmlBody = loadEmailTemplate('email-template.html');

  surveyDocs.forEach(async (surveyDoc: ISurveyDoc) => {
    await mailgunClient.messages
      .create(process.env.MAILGUN_DOMAIN!, {
        from: process.env.MAILGUN_FROM!,
        to: surveyDoc.userEmail,
        subject:
          'Help Us Strengthen the InBev–Agency Partnership – 3 Minutes Tops',
        html: processHtmlTemplate(htmlBody, {
          FirstName: surveyDoc.userName,
          PassCode: '', // Note: Passcode is now separate from survey
          SurveyLink: `${process.env.SURVEY_LINK}${surveyDoc._id.toString()}`,
        }),
      })
      .catch((error) => {
        console.log(error);
      });
  });
  return;
}
