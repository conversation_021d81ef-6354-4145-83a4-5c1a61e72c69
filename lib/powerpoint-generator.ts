import PptxGenJS from 'pptxgenjs';
import { getWaveStatistics } from './api/waves';
import { getSurveysByWave } from './api/surveys';
import { ISurveyDoc } from './models/survey';
import { ResponseModel, IResponseDoc } from './models/response';
import { WaveModel } from './models/wave';
import dbConnect from './mongodb';

interface DashboardFilters {
  waves: string[];
  agency: string;
  brand: string;
  region: string;
}

// Helper function to convert wave names to wave IDs
async function getWaveIdByName(waveName: string): Promise<string | null> {
  await dbConnect();
  const wave = await WaveModel.findOne({ name: waveName });
  return wave ? wave._id.toString() : null;
}

export async function generateDashboardPowerPoint(
  filters: DashboardFilters
): Promise<Buffer> {
  const pptx = new PptxGenJS();

  // Set presentation properties
  pptx.author = 'Echo360 Platform';
  pptx.company = 'Echo360';
  pptx.title = 'Dashboard Report';
  pptx.subject = 'Survey Analytics Dashboard';

  // Convert wave names to wave IDs
  const waveIds: string[] = [];
  for (const waveName of filters.waves) {
    const waveId = await getWaveIdByName(waveName);
    if (waveId) {
      waveIds.push(waveId);
    } else {
      console.error(`Wave not found: ${waveName}`);
    }
  }

  // Fetch data based on filters
  let allSurveys: ISurveyDoc[] = [];
  if (waveIds.length > 0) {
    for (const waveId of waveIds) {
      try {
        const surveys = await getSurveysByWave(waveId);
        allSurveys = allSurveys.concat(surveys);
      } catch (error) {
        console.error(`Error fetching surveys for wave ${waveId}:`, error);
      }
    }
  }

  // Apply additional filters
  const filteredSurveys = allSurveys.filter(survey => {
    return (
      (filters.agency === 'All Agencies' || survey.agencyName === filters.agency) &&
      (filters.brand === 'All Brands' || survey.brand === filters.brand) &&
      (filters.region === 'All Regions' || survey.region === filters.region)
    );
  });

  // Slide 1: Title Slide
  const titleSlide = pptx.addSlide();
  titleSlide.addText('Echo360 Dashboard Report', {
    x: 1,
    y: 2,
    w: 8,
    h: 1.5,
    fontSize: 32,
    bold: true,
    align: 'center',
    color: '363636'
  });

  const timestamp = new Date().toLocaleString();
  titleSlide.addText(`Generated: ${timestamp}`, {
    x: 1,
    y: 4,
    w: 8,
    h: 0.5,
    fontSize: 16,
    align: 'center',
    color: '666666'
  });

  // Filter information
  const filterText = [
    `Waves: ${filters.waves.length > 0 ? filters.waves.join(', ') : 'All Waves'}`,
    `Agency: ${filters.agency}`,
    `Brand: ${filters.brand}`,
    `Region: ${filters.region}`
  ].join('\n');

  titleSlide.addText(filterText, {
    x: 1,
    y: 5,
    w: 8,
    h: 2,
    fontSize: 14,
    align: 'center',
    color: '666666'
  });

  // Slide 2: Statistics Panel
  const statsSlide = pptx.addSlide();
  statsSlide.addText('Dashboard Statistics', {
    x: 0.5,
    y: 0.5,
    w: 9,
    h: 1,
    fontSize: 24,
    bold: true,
    color: '363636'
  });

  // Get responses for the filtered surveys
  const surveyIds = filteredSurveys.map(s => s._id);
  const allResponses: IResponseDoc[] = await ResponseModel.find({
    surveyId: { $in: surveyIds }
  });

  const totalSurveys = filteredSurveys.length;
  const completedSurveys = allResponses.length;
  const completionRate = totalSurveys > 0 ? (completedSurveys / totalSurveys * 100).toFixed(1) : '0';

  const avgScore = allResponses.length > 0
    ? (allResponses.reduce((sum, r) => sum + (r.overallRating || 0), 0) / allResponses.length).toFixed(1)
    : '0';

  const statsData: PptxGenJS.TableRow[] = [
    [{ text: 'Metric' }, { text: 'Value' }],
    [{ text: 'Total Surveys' }, { text: totalSurveys.toString() }],
    [{ text: 'Completed Surveys' }, { text: completedSurveys.toString() }],
    [{ text: 'Completion Rate' }, { text: `${completionRate}%` }],
    [{ text: 'Average Score' }, { text: avgScore }]
  ];

  statsSlide.addTable(statsData, {
    x: 1,
    y: 2,
    w: 8,
    h: 4,
    fontSize: 14,
    border: { pt: 1, color: 'CFCFCF' },
    fill: { color: 'F7F7F7' },
    color: '363636'
  });

  // Slide 3: Bar Chart Data Panel
  const chartSlide = pptx.addSlide();
  chartSlide.addText('Survey Response Data by Agency', {
    x: 0.5,
    y: 0.5,
    w: 9,
    h: 1,
    fontSize: 24,
    bold: true,
    color: '363636'
  });

  // Group by agency for chart representation
  const agencyData = filteredSurveys.reduce((acc, survey) => {
    const agency = survey.agencyName || 'Unknown';
    if (!acc[agency]) {
      acc[agency] = { total: 0, completed: 0 };
    }
    acc[agency].total++;
    // Check if this survey has responses
    const hasResponse = allResponses.some(r => r.surveyId.toString() === survey._id.toString());
    if (hasResponse) {
      acc[agency].completed++;
    }
    return acc;
  }, {} as Record<string, { total: number; completed: number }>);

  const chartData: PptxGenJS.TableRow[] = [[
    { text: 'Agency' },
    { text: 'Total' },
    { text: 'Completed' },
    { text: 'Completion Rate' }
  ]];
  Object.entries(agencyData).forEach(([agency, data]) => {
    const rate = data.total > 0 ? (data.completed / data.total * 100).toFixed(1) : '0';
    chartData.push([
      { text: agency },
      { text: data.total.toString() },
      { text: data.completed.toString() },
      { text: `${rate}%` }
    ]);
  });

  if (chartData.length > 1) {
    chartSlide.addTable(chartData, {
      x: 0.5,
      y: 2,
      w: 9,
      h: 4,
      fontSize: 12,
      border: { pt: 1, color: 'CFCFCF' },
      fill: { color: 'F7F7F7' },
      color: '363636'
    });
  } else {
    chartSlide.addText('No survey data available for the selected filters.', {
      x: 1,
      y: 3,
      w: 8,
      h: 1,
      fontSize: 16,
      align: 'center',
      color: '666666'
    });
  }

  // Slide 4: Comments Panel
  const comments: string[] = [];
  allResponses.forEach(response => {
    for (let i = 1; i <= 5; i++) {
      const comment = response[`q${i}Comment` as keyof IResponseDoc] as string;
      if (comment && comment.trim()) {
        comments.push(comment.trim());
      }
    }
  });

  if (comments.length > 0) {
    const commentsSlide = pptx.addSlide();
    commentsSlide.addText('Survey Comments', {
      x: 0.5,
      y: 0.5,
      w: 9,
      h: 1,
      fontSize: 24,
      bold: true,
      color: '363636'
    });

    const commentsText = comments
      .slice(0, 10)
      .map((comment, index) => `${index + 1}. ${comment}`)
      .join('\n\n');

    commentsSlide.addText(commentsText, {
      x: 0.5,
      y: 2,
      w: 9,
      h: 5,
      fontSize: 12,
      color: '363636',
      valign: 'top'
    });
  }

  // Slide 5: Wave History Panel
  if (filters.waves.length > 0) {
    const waveSlide = pptx.addSlide();
    waveSlide.addText('Wave History', {
      x: 0.5,
      y: 0.5,
      w: 9,
      h: 1,
      fontSize: 24,
      bold: true,
      color: '363636'
    });

    const waveData: PptxGenJS.TableRow[] = [[
      { text: 'Wave' },
      { text: 'Status' },
      { text: 'Total Surveys' },
      { text: 'Completed' },
      { text: 'Average Score' }
    ]];

    for (const waveName of filters.waves) {
      try {
        const waveId = await getWaveIdByName(waveName);
        if (waveId) {
          const waveStats = await getWaveStatistics(waveId);
          waveData.push([
            { text: waveStats.waveName || waveName },
            { text: waveStats.status || 'Unknown' },
            { text: (waveStats.totalSurveys || 0).toString() },
            { text: (waveStats.completedSurveys || 0).toString() },
            { text: waveStats.averageScore ? waveStats.averageScore.toFixed(1) : 'N/A' }
          ]);
        } else {
          waveData.push([
            { text: waveName },
            { text: 'Not Found' },
            { text: 'N/A' },
            { text: 'N/A' },
            { text: 'N/A' }
          ]);
        }
      } catch (error) {
        console.error(`Error fetching wave stats for ${waveName}:`, error);
        waveData.push([
          { text: waveName },
          { text: 'Error' },
          { text: 'N/A' },
          { text: 'N/A' },
          { text: 'N/A' }
        ]);
      }
    }

    waveSlide.addTable(waveData, {
      x: 0.5,
      y: 2,
      w: 9,
      h: 4,
      fontSize: 12,
      border: { pt: 1, color: 'CFCFCF' },
      fill: { color: 'F7F7F7' },
      color: '363636'
    });
  }

  // Generate and return the PowerPoint buffer
  return pptx.write({ outputType: 'nodebuffer' }) as Promise<Buffer>;
}
