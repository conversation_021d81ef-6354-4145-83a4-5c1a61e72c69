import mongoose from 'mongoose';

const MONGODB_URI = process.env.MONGODB_URI as string;
const options = {};

if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable.');
}

let isDbConnected: boolean = false;

export default async function dbConnect() {
  if (process.env.NODE_ENV === 'development') {
    const globalWithMongoDb = global as typeof globalThis & {
      isDbConnected: boolean;
    };
    if (globalWithMongoDb.isDbConnected) return;

    await mongoose.connect(MONGODB_URI, options);

    globalWithMongoDb.isDbConnected = true;
  } else {
    if (isDbConnected) return;

    await mongoose.connect(MONGODB_URI!, options);

    isDbConnected = true;
  }

  console.log('✅ MongoDB connected');
}
