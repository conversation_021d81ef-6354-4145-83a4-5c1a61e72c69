'use server';

import dbConnect from '../mongodb';
import { Types } from 'mongoose';
import { WaveModel, IWaveInput, IWaveDoc } from '../models/wave';
import {
  SurveyModel,
  ISurveyImport,
  ISurveyInput,
} from '../models/survey';
import {
  UserPasscodeModel,
  IUserPasscodeInput,
} from '../models/user-passcode';
import { getLatestWaveSurveyDocs } from './surveys';
import generatePasscode from '../../util/generate-passcode';
import { formatDate } from '../../util/format-date';

export interface IWaveVm {
  id: string;
  name: string;
  status: string;
  dateInitiated: string;
  surveyCount?: number;
}

// Map a Wave from Document to View Model
export async function fromWaveDocToVm(doc: IWaveDoc): Promise<IWaveVm> {
  const surveyCount = await SurveyModel.countDocuments({ waveId: doc._id });
  
  return {
    id: doc._id.toString(),
    name: doc.name,
    status: doc.status,
    dateInitiated: formatDate(doc.createdAt.toString()),
    surveyCount,
  };
}

// Get Wave View Models
export async function getWaveVms(includeSeed: boolean = true): Promise<IWaveVm[]> {
  await dbConnect();

  const filter = includeSeed ? {} : { status: { $ne: 'seed' } };
  const data: IWaveDoc[] = await WaveModel.find(filter).sort({ createdAt: -1 });

  return Promise.all(data.map(fromWaveDocToVm));
}

// Get Wave View Models for dashboard (excludes seed data)
export async function getDashboardWaveVms(): Promise<IWaveVm[]> {
  return getWaveVms(false);
}

// Get Seed Wave View Models for creating new waves
export async function getSeedWaveVms(): Promise<IWaveVm[]> {
  await dbConnect();

  const data: IWaveDoc[] = await WaveModel.find({ status: 'seed' }).sort({ createdAt: -1 });

  return Promise.all(data.map(fromWaveDocToVm));
}

// Add a new Wave Document and Survey Documents given a Wave Input and Survey Imports
export async function addWave(
  wave: IWaveInput,
  surveyImports: ISurveyImport[]
): Promise<{ success: boolean; waveId?: string }> {
  await dbConnect();

  if (!wave.name) {
    return Promise.reject('Name is required');
  }

  await WaveModel.exists({ name: wave.name }).then((res) => {
    if (res) return Promise.reject('Name already exists');
  });

  if (!surveyImports.length) {
    return Promise.reject('No surveys provided');
  }

  let newWaveId: Types.ObjectId = new Types.ObjectId();

  // Create the wave
  const newWave = await WaveModel.create({
    name: wave.name,
    status: wave.status || 'seed',
  });
  newWaveId = newWave._id;

  try {
    // Create surveys
    const surveyInputs: ISurveyInput[] = surveyImports.map((surveyImport) => ({
      ...surveyImport,
      waveId: newWaveId,
    }));

    await SurveyModel.insertMany(surveyInputs);

    // Only generate passcodes for non-seed waves (actual waves that will send emails)
    if (newWave.status !== 'seed') {
      // Generate passcodes for unique users
      const uniqueUsers = new Map<string, ISurveyImport>();
      surveyImports.forEach(survey => {
        // Only create passcodes for surveys with valid email addresses
        if (survey.userEmail && survey.userEmail.trim() !== '') {
          uniqueUsers.set(survey.userEmail, survey);
        }
      });

      const passcodeInputs: IUserPasscodeInput[] = Array.from(uniqueUsers.values()).map(user => ({
        userEmail: user.userEmail!,
        passcode: generatePasscode(),
        waveId: newWaveId,
        isActive: true,
      }));

      await UserPasscodeModel.insertMany(passcodeInputs);
    }

    // Return success without redirect - let the client handle navigation
    return { success: true, waveId: newWaveId.toString() };

  } catch (err: unknown) {
    // Rollback wave creation if surveys/passcodes fail
    await WaveModel.findByIdAndDelete(newWaveId);
    const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
    return Promise.reject(errorMessage);
  }
}

// Update wave status
export async function updateWaveStatus(
  waveId: string, 
  status: 'draft' | 'launched' | 'active' | 'completed' | 'archived' | 'seed'
): Promise<void> {
  await dbConnect();
  
  await WaveModel.findByIdAndUpdate(waveId, { status });
}

// Launch wave (set to active and send emails)
export async function launchWave(waveId: string): Promise<void> {
  await dbConnect();
  
  // Update status to active
  await WaveModel.findByIdAndUpdate(waveId, { status: 'active' });
  
  // Get all passcodes for this wave (for future email functionality)
  // const passcodes = await UserPasscodeModel.find({ waveId });
  
  // Send emails (you'll need to update this based on your email structure)
  // sendSimpleMessages(passcodes);
}

export async function emailBlastLatestWave(): Promise<undefined> {
  // For future implementation - get survey docs and send emails
  // const surveyDocs: ISurveyDoc[] = await getLatestWaveSurveyDocs();
  // sendSimpleMessages(surveyDocs);

  // Placeholder implementation
  await getLatestWaveSurveyDocs(); // This will be used when email functionality is enabled
}

// Get wave statistics for reporting
export async function getWaveStatistics(waveId: string): Promise<{
  waveName: string;
  status: string;
  totalSurveys: number;
  completedSurveys: number;
  averageScore?: number;
}> {
  await dbConnect();

  const wave = await WaveModel.findById(waveId);
  if (!wave) {
    throw new Error(`Wave not found: ${waveId}`);
  }

  const surveys = await SurveyModel.find({ waveId });
  const totalSurveys = surveys.length;

  // Count completed surveys (those with responses)
  const completedSurveys = surveys.filter(survey =>
    survey.responses && survey.responses.length > 0
  ).length;

  // Calculate average score
  const allResponses = surveys.flatMap(survey => survey.responses || []);
  const averageScore = allResponses.length > 0
    ? allResponses.reduce((sum, response) => sum + (response.overallRating || 0), 0) / allResponses.length
    : undefined;

  return {
    waveName: wave.name,
    status: wave.status,
    totalSurveys,
    completedSurveys,
    averageScore
  };
}
