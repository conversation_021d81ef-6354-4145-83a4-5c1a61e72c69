'use server';

import dbConnect from '../mongodb';
import {
  ResponseModel,
  IResponseInput,
  IResponseDoc,
} from '../models/response';
import { SurveyModel } from '../models/survey';
import { WaveModel } from '../models/wave';
import { UserPasscodeModel } from '../models/user-passcode';
import { formatDate } from '../../util/format-date';

export interface IResponseVm {
  id: string;
  surveyId: string;
  assessorName: string;
  assessorType: string;
  q1Score?: number;
  q1Comment?: string;
  q2Score?: number;
  q2Comment?: string;
  q3Score?: number;
  q3Comment?: string;
  q4Score?: number;
  q4Comment?: string;
  q5Score?: number;
  q5Comment?: string;
  npsScore?: number;
  overallRating?: number;
  submittedAt: string;
}

export interface IResponseAnalytics {
  agencyName: string;
  agencyType: string;
  brand: string;
  region: string;
  country: string;
  assessmentType: string;
  averageScore: number;
  responseCount: number;
  npsScore?: number;
}

export interface IResponseImport {
  agency: string;
  type: string;
  zone: string;
  country: string;
  brand: string;
  brandCategory: string;
  reportingMonth: string;
  account: string;
  assessorName: string;
  assessorType: string;
  section: string;
  score: number;
  comment: string;
  evaluationName: string;
}

// Map Response from Document to View Model
export async function fromResponseDocToVm(doc: IResponseDoc): Promise<IResponseVm> {
  return {
    id: doc._id.toString(),
    surveyId: doc.surveyId.toString(),
    assessorName: doc.assessorName,
    assessorType: doc.assessorType,
    q1Score: doc.q1Score,
    q1Comment: doc.q1Comment || '',
    q2Score: doc.q2Score,
    q2Comment: doc.q2Comment || '',
    q3Score: doc.q3Score,
    q3Comment: doc.q3Comment || '',
    q4Score: doc.q4Score,
    q4Comment: doc.q4Comment || '',
    q5Score: doc.q5Score,
    q5Comment: doc.q5Comment || '',
    npsScore: doc.npsScore,
    overallRating: doc.overallRating,
    submittedAt: formatDate(doc.createdAt.toString()),
  };
}

// Calculate NPS score from responses
function calculateNPS(q1Score: number): number {
  // NPS calculation: Promoters (4-5) - Detractors (1-2)
  // This is a simplified version - you may want to adjust based on your specific NPS formula
  if (q1Score >= 4) return 1; // Promoter
  if (q1Score <= 2) return -1; // Detractor
  return 0; // Neutral
}

// Calculate overall rating (average of all question scores)
function calculateOverallRating(scores: number[]): number {
  return scores.reduce((sum, score) => sum + score, 0) / scores.length;
}

// Import responses from CSV data
export async function importResponses(responseImports: IResponseImport[]): Promise<{
  imported: number;
  errors: string[];
  created: {
    waves: number;
    surveys: number;
  };
}> {
  await dbConnect();

  const errors: string[] = [];
  let imported = 0;
  let createdWaves = 0;
  let createdSurveys = 0;

  // Helper function to parse reporting month to Date
  const parseReportingMonth = (reportingMonth: string): Date => {
    // Try different date formats commonly used in CSV exports
    const formats = [
      // MM/yyyy format (e.g., "03/2023")
      /^(\d{1,2})\/(\d{4})$/,
      // MM-yyyy format (e.g., "03-2023")
      /^(\d{1,2})-(\d{4})$/,
      // yyyy-MM format (e.g., "2023-03")
      /^(\d{4})-(\d{1,2})$/,
      // Mon YYYY format (e.g., "Mar 2023", "March 2023")
      /^(\w{3,9})\s+(\d{4})$/,
    ];

    for (const format of formats) {
      const match = reportingMonth.match(format);
      if (match) {
        if (format.source.includes('(\\d{4})-(\\d{1,2})')) {
          // yyyy-MM format
          const year = parseInt(match[1]);
          const month = parseInt(match[2]) - 1; // JS months are 0-indexed
          return new Date(year, month, 1);
        } else if (format.source.includes('(\\d{1,2})')) {
          // MM/yyyy or MM-yyyy format
          const month = parseInt(match[1]) - 1; // JS months are 0-indexed
          const year = parseInt(match[2]);
          return new Date(year, month, 1);
        } else if (format.source.includes('(\\w{3,9})')) {
          // Month name format
          const monthName = match[1];
          const year = parseInt(match[2]);
          const monthMap: { [key: string]: number } = {
            'jan': 0, 'january': 0,
            'feb': 1, 'february': 1,
            'mar': 2, 'march': 2,
            'apr': 3, 'april': 3,
            'may': 4,
            'jun': 5, 'june': 5,
            'jul': 6, 'july': 6,
            'aug': 7, 'august': 7,
            'sep': 8, 'september': 8,
            'oct': 9, 'october': 9,
            'nov': 10, 'november': 10,
            'dec': 11, 'december': 11,
          };
          const month = monthMap[monthName.toLowerCase()];
          if (month !== undefined) {
            return new Date(year, month, 1);
          }
        }
      }
    }

    // If no format matches, try standard Date parsing as fallback
    const fallbackDate = new Date(reportingMonth);
    if (!isNaN(fallbackDate.getTime())) {
      return fallbackDate;
    }

    // If all parsing fails, return current date and log warning
    console.warn(`Unable to parse reporting month: "${reportingMonth}", using current date`);
    return new Date();
  };

  // Group responses by assessor and survey details to combine into single response records
  const responseGroups = new Map<string, IResponseImport[]>();
  
  responseImports.forEach(item => {
    const key = `${item.assessorName}-${item.agency}-${item.brand}-${item.country}-${item.assessorType}`;
    if (!responseGroups.has(key)) {
      responseGroups.set(key, []);
    }
    responseGroups.get(key)!.push(item);
  });

  // Track created waves to avoid duplicates and collect reporting dates for each wave
  const createdWaveNames = new Set<string>();
  const waveReportingDates = new Map<string, Date[]>();

  // First pass: collect all reporting dates for each wave
  for (const [, responses] of responseGroups) {
    const firstResponse = responses[0];
    const reportingDate = parseReportingMonth(firstResponse.reportingMonth);
    
    if (!waveReportingDates.has(firstResponse.evaluationName)) {
      waveReportingDates.set(firstResponse.evaluationName, []);
    }
    waveReportingDates.get(firstResponse.evaluationName)!.push(reportingDate);
  }

  for (const [key, responses] of responseGroups) {
    try {
      const firstResponse = responses[0];
      const assessmentType = firstResponse.assessorType === 'Agency' 
        ? 'Agency-on-Anheuser Busch In-Bev' 
        : 'Anheuser Busch In-Bev-on-Agency';

      // First, ensure the wave exists (create if needed)
      let wave = await WaveModel.findOne({ name: firstResponse.evaluationName });
      if (!wave && !createdWaveNames.has(firstResponse.evaluationName)) {
        // Get the earliest reporting date for this wave
        const reportingDates = waveReportingDates.get(firstResponse.evaluationName) || [];
        const earliestDate = reportingDates.length > 0 ? new Date(Math.min(...reportingDates.map(d => d.getTime()))) : new Date();
        
        wave = await WaveModel.create({
          name: firstResponse.evaluationName,
          status: 'completed', // Historical data is typically completed
          createdAt: earliestDate,
          updatedAt: earliestDate
        });
        createdWaves++;
        createdWaveNames.add(firstResponse.evaluationName);
      }

      if (!wave) {
        // Wave was created by another iteration
        wave = await WaveModel.findOne({ name: firstResponse.evaluationName });
      }

      // Create a unique survey for each response (1:1 relationship)
      // Include assessorName and assessorType in the survey lookup to ensure uniqueness
      let survey = await SurveyModel.findOne({
        agencyName: firstResponse.agency,
        brand: firstResponse.brand,
        country: firstResponse.country || 'Unknown',
        assessmentType: assessmentType,
        userName: firstResponse.assessorName,
        waveId: wave!._id
      });

      if (!survey) {
        // Create the missing survey with empty email for historical imports
        survey = await SurveyModel.create({
          waveId: wave!._id,
          accountName: firstResponse.account,
          agencyName: firstResponse.agency,
          agencyType: firstResponse.type,
          brand: firstResponse.brand,
          country: firstResponse.country || 'Unknown',
          region: firstResponse.zone || 'Unknown',
          assessmentType: assessmentType,
          userName: firstResponse.assessorName,
          userEmail: '', // Empty email for historical imports
          userStatus: 'Active',
          inScope: 'Yes',
          notes: `Auto-created from response import for ${firstResponse.evaluationName}`
        });
        createdSurveys++;
      }

      // Check if response already exists for this survey
      const existingResponse = await ResponseModel.findOne({ 
        surveyId: survey._id
      });
      if (existingResponse) {
        errors.push(`Response already exists for survey: ${firstResponse.assessorName} - ${firstResponse.agency} - ${firstResponse.brand}`);
        continue;
      }

      // Map responses to questions (handle various section formats)
      const questionMap: { [key: string]: number } = {
        '1 People and Partnership': 1,
        '2 Account Management and Process': 2,
        '3 Strategy': 3,
        '4 Creativity': 4,
        '5 Media Planning and Execution': 5,
        // Handle if sections are just numbers
        '1': 1,
        '2': 2,
        '3': 3,
        '4': 4,
        '5': 5,
        // Handle other possible formats
        'People and Partnership': 1,
        'Account Management and Process': 2,
        'Strategy': 3,
        'Creativity': 4,
        'Media Planning and Execution': 5,
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const responseData: any = {
        surveyId: survey._id,
        assessorName: firstResponse.assessorName,
        assessorType: firstResponse.assessorType,
        // Keep the natural createdAt/updatedAt for responses (when they actually responded)
        // Don't modify these - let Mongoose handle them automatically
      };

      // Track which questions have actual data from CSV
      const answeredQuestions = new Set<number>();

      // Fill in the questions based on section mapping
      responses.forEach(resp => {
        const questionNum = questionMap[resp.section];
        if (questionNum && resp.score >= 1 && resp.score <= 5) { // Valid score range 1-5
          responseData[`q${questionNum}Score`] = resp.score;
          responseData[`q${questionNum}Comment`] = resp.comment;
          answeredQuestions.add(questionNum);
        }
      });

      // Check if we have any actual responses
      if (answeredQuestions.size > 0) {
        // Only calculate derived fields using the scores that were actually provided
        const providedScores: number[] = [];
        for (let i = 1; i <= 5; i++) {
          if (answeredQuestions.has(i)) {
            providedScores.push(responseData[`q${i}Score`]);
          }
        }
        
        // Calculate NPS from q1 if it exists, otherwise skip
        if (answeredQuestions.has(1)) {
          responseData.npsScore = calculateNPS(responseData.q1Score);
        }
        
        // Calculate overall rating only from answered questions
        if (providedScores.length > 0) {
          responseData.overallRating = calculateOverallRating(providedScores);
        }

        await ResponseModel.create(responseData);
        
        // Update survey status
        await SurveyModel.findByIdAndUpdate(survey._id, {
          userStatus: 'Submitted'
        });

        imported++;
      } else {
        errors.push(`No valid scores found for ${firstResponse.assessorName} - ${firstResponse.agency} - ${firstResponse.brand}`);
      }

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(`Error processing ${key}: ${errorMessage}`);
    }
  }

  return { 
    imported, 
    errors,
    created: {
      waves: createdWaves,
      surveys: createdSurveys
    }
  };
}

// Submit a new response
export async function submitResponse(responseInput: IResponseInput): Promise<void> {
  await dbConnect();

  // Calculate derived fields only from provided scores
  const providedScores = [
    responseInput.q1Score,
    responseInput.q2Score,
    responseInput.q3Score,
    responseInput.q4Score,
    responseInput.q5Score,
  ].filter((score): score is number => score !== undefined);

  const responseData = {
    ...responseInput,
    // Only calculate NPS if q1Score is provided
    npsScore: responseInput.q1Score ? calculateNPS(responseInput.q1Score) : undefined,
    // Only calculate overall rating if we have scores
    overallRating: providedScores.length > 0 ? calculateOverallRating(providedScores) : undefined,
  };

  await ResponseModel.create(responseData);

  // Update survey status to completed
  await SurveyModel.findByIdAndUpdate(responseInput.surveyId, {
    userStatus: 'Submitted'
  });
}

// Get response by survey ID
export async function getResponseBySurvey(surveyId: string): Promise<IResponseVm | null> {
  await dbConnect();
  
  const response = await ResponseModel.findOne({ surveyId });
  
  if (!response) {
    return null;
  }

  return fromResponseDocToVm(response);
}

// Get all responses for analytics
export async function getResponseAnalytics(filters?: {
  agencyName?: string;
  brand?: string;
  region?: string;
  country?: string;
  assessmentType?: string;
}): Promise<IResponseAnalytics[]> {
  await dbConnect();

  // Build aggregation pipeline
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const pipeline: any[] = [
    // Join with surveys to get agency/brand info
    {
      $lookup: {
        from: 'surveys',
        localField: 'surveyId',
        foreignField: '_id',
        as: 'survey'
      }
    },
    { $unwind: '$survey' },
    // Join with waves to filter out seed data
    {
      $lookup: {
        from: 'waves',
        localField: 'survey.waveId',
        foreignField: '_id',
        as: 'wave'
      }
    },
    { $unwind: '$wave' },
    // Filter out seed waves
    {
      $match: {
        'wave.status': { $ne: 'seed' }
      }
    }
  ];

  // Add filters if provided
  if (filters) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const matchConditions: any = {};
    if (filters.agencyName) matchConditions['survey.agencyName'] = filters.agencyName;
    if (filters.brand) matchConditions['survey.brand'] = filters.brand;
    if (filters.region) matchConditions['survey.country'] = filters.region;
    if (filters.country) matchConditions['survey.country'] = filters.country;
    if (filters.assessmentType) matchConditions['survey.assessmentType'] = filters.assessmentType;
    
    if (Object.keys(matchConditions).length > 0) {
      pipeline.push({ $match: matchConditions });
    }
  }

  // Group and calculate averages
  pipeline.push({
    $group: {
      _id: {
        agencyName: '$survey.agencyName',
        agencyType: '$survey.agencyType',
        brand: '$survey.brand',
        region: '$survey.country',
        country: '$survey.country',
        assessmentType: '$survey.assessmentType',
      },
      averageScore: {
        $avg: {
          $avg: ['$q1Score', '$q2Score', '$q3Score', '$q4Score', '$q5Score']
        }
      },
      responseCount: { $sum: 1 },
      avgNpsScore: { $avg: '$npsScore' },
    }
  });

  // Format output
  pipeline.push({
    $project: {
      _id: 0,
      agencyName: '$_id.agencyName',
      agencyType: '$_id.agencyType',
      brand: '$_id.brand',
      region: '$_id.region',
      country: '$_id.country',
      assessmentType: '$_id.assessmentType',
      averageScore: { $round: ['$averageScore', 2] },
      responseCount: 1,
      npsScore: { $round: ['$avgNpsScore', 2] },
    }
  });

  return await ResponseModel.aggregate(pipeline);
}

// Get response statistics
export async function getResponseStats(filters?: {
  agencyName?: string;
  brand?: string;
  region?: string;
  country?: string;
  assessmentType?: string;
  wave?: string;
}): Promise<{
  totalResponses: number;
  averageScore: number;
  completionRate: number;
}> {
  await dbConnect();

  // Build aggregation pipeline for responses
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const responsePipeline: any[] = [
    // Join with surveys
    {
      $lookup: {
        from: 'surveys',
        localField: 'surveyId',
        foreignField: '_id',
        as: 'survey'
      }
    },
    { $unwind: '$survey' },
    // Join with waves to filter out seed data
    {
      $lookup: {
        from: 'waves',
        localField: 'survey.waveId',
        foreignField: '_id',
        as: 'wave'
      }
    },
    { $unwind: '$wave' },
    // Filter out seed waves
    {
      $match: {
        'wave.status': { $ne: 'seed' }
      }
    }
  ];

  // Add filters if provided
  if (filters) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const matchConditions: any = {};
    if (filters.agencyName) matchConditions['survey.agencyName'] = filters.agencyName;
    if (filters.brand) matchConditions['survey.brand'] = filters.brand;
    if (filters.region) matchConditions['survey.country'] = filters.region;
    if (filters.country) matchConditions['survey.country'] = filters.country;
    if (filters.assessmentType) matchConditions['survey.assessmentType'] = filters.assessmentType;
    if (filters.wave) {
      // Handle multiple waves (comma-separated)
      const waves = filters.wave.split(',').map(w => w.trim());
      if (waves.length === 1) {
        matchConditions['wave.name'] = waves[0];
      } else {
        matchConditions['wave.name'] = { $in: waves };
      }
    }
    
    if (Object.keys(matchConditions).length > 0) {
      responsePipeline.push({ $match: matchConditions });
    }
  }

  // Count responses and calculate average score
  responsePipeline.push({
    $group: {
      _id: null,
      totalResponses: { $sum: 1 },
      averageScore: {
        $avg: {
          $avg: ['$q1Score', '$q2Score', '$q3Score', '$q4Score', '$q5Score']
        }
      }
    }
  });

  // Build aggregation pipeline for surveys (for completion rate)
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const surveyPipeline: any[] = [
    // Join with waves to filter out seed data
    {
      $lookup: {
        from: 'waves',
        localField: 'waveId',
        foreignField: '_id',
        as: 'wave'
      }
    },
    { $unwind: '$wave' },
    // Filter out seed waves
    {
      $match: {
        'wave.status': { $ne: 'seed' }
      }
    }
  ];

  // Add same filters to survey pipeline
  if (filters) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const matchConditions: any = {};
    if (filters.agencyName) matchConditions['agencyName'] = filters.agencyName;
    if (filters.brand) matchConditions['brand'] = filters.brand;
    if (filters.region) matchConditions['country'] = filters.region;
    if (filters.country) matchConditions['country'] = filters.country;
    if (filters.assessmentType) matchConditions['assessmentType'] = filters.assessmentType;
    if (filters.wave) {
      // Handle multiple waves (comma-separated)
      const waves = filters.wave.split(',').map(w => w.trim());
      if (waves.length === 1) {
        matchConditions['wave.name'] = waves[0];
      } else {
        matchConditions['wave.name'] = { $in: waves };
      }
    }
    
    if (Object.keys(matchConditions).length > 0) {
      surveyPipeline.push({ $match: matchConditions });
    }
  }

  surveyPipeline.push({
    $group: {
      _id: null,
      totalSurveys: { $sum: 1 }
    }
  });

  // Execute both pipelines
  const [responseStatsResult, surveyStatsResult] = await Promise.all([
    ResponseModel.aggregate(responsePipeline),
    SurveyModel.aggregate(surveyPipeline)
  ]);

  const totalResponses = responseStatsResult.length > 0 ? responseStatsResult[0].totalResponses : 0;
  const averageScore = responseStatsResult.length > 0 ? responseStatsResult[0].averageScore : 0;
  const totalSurveys = surveyStatsResult.length > 0 ? surveyStatsResult[0].totalSurveys : 0;
  const completionRate = totalSurveys > 0 ? (totalResponses / totalSurveys) * 100 : 0;

  return {
    totalResponses,
    averageScore: Math.round(averageScore * 100) / 100,
    completionRate: Math.round(completionRate * 100) / 100,
  };
}

// Clear all responses for a specific wave (useful for re-importing)
export async function clearResponsesForWave(waveName: string): Promise<{
  deletedResponses: number;
  deletedSurveys: number;
  deletedPasscodes: number;
}> {
  await dbConnect();

  // Find the wave
  const wave = await WaveModel.findOne({ name: waveName });
  if (!wave) {
    return { deletedResponses: 0, deletedSurveys: 0, deletedPasscodes: 0 };
  }

  // Find all surveys for this wave
  const surveys = await SurveyModel.find({ waveId: wave._id });
  const surveyIds = surveys.map(s => s._id);

  // Delete all responses for these surveys
  const deletedResponses = await ResponseModel.deleteMany({ 
    surveyId: { $in: surveyIds } 
  });

  // Delete all surveys for this wave
  const deletedSurveys = await SurveyModel.deleteMany({ waveId: wave._id });

  // Delete all user passcodes for this wave
  const deletedPasscodes = await UserPasscodeModel.deleteMany({ waveId: wave._id });

  // Delete the wave itself
  await WaveModel.findByIdAndDelete(wave._id);

  return { 
    deletedResponses: deletedResponses.deletedCount || 0,
    deletedSurveys: deletedSurveys.deletedCount || 0,
    deletedPasscodes: deletedPasscodes.deletedCount || 0
  };
}

// Get detailed question analytics for dashboard charts
export async function getQuestionAnalytics(filters?: {
  agencyName?: string;
  brand?: string;
  region?: string;
  country?: string;
  assessmentType?: string;
  wave?: string;
}): Promise<{
  q1Avg: number;
  q2Avg: number;
  q3Avg: number;
  q4Avg: number;
  q5Avg: number;
  npsAvg: number;
  responseCount: number;
}> {
  await dbConnect();

  // Build aggregation pipeline
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const pipeline: any[] = [
    // Join with surveys to get agency/brand info
    {
      $lookup: {
        from: 'surveys',
        localField: 'surveyId',
        foreignField: '_id',
        as: 'survey'
      }
    },
    { $unwind: '$survey' },
    // Join with waves to filter out seed data
    {
      $lookup: {
        from: 'waves',
        localField: 'survey.waveId',
        foreignField: '_id',
        as: 'wave'
      }
    },
    { $unwind: '$wave' },
    // Filter out seed waves
    {
      $match: {
        'wave.status': { $ne: 'seed' }
      }
    }
  ];

  // Add filters if provided
  if (filters) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const matchConditions: any = {};
    if (filters.agencyName) matchConditions['survey.agencyName'] = filters.agencyName;
    if (filters.brand) matchConditions['survey.brand'] = filters.brand;
    if (filters.region) matchConditions['survey.country'] = filters.region;
    if (filters.country) matchConditions['survey.country'] = filters.country;
    if (filters.assessmentType) matchConditions['survey.assessmentType'] = filters.assessmentType;
    if (filters.wave) {
      // Handle multiple waves (comma-separated)
      const waves = filters.wave.split(',').map(w => w.trim());
      if (waves.length === 1) {
        matchConditions['wave.name'] = waves[0];
      } else {
        matchConditions['wave.name'] = { $in: waves };
      }
    }
    
    if (Object.keys(matchConditions).length > 0) {
      pipeline.push({ $match: matchConditions });
    }
  }

  // Group and calculate averages for each question
  pipeline.push({
    $group: {
      _id: null,
      q1Avg: { $avg: '$q1Score' },
      q2Avg: { $avg: '$q2Score' },
      q3Avg: { $avg: '$q3Score' },
      q4Avg: { $avg: '$q4Score' },
      q5Avg: { $avg: '$q5Score' },
      npsAvg: { $avg: '$npsScore' },
      responseCount: { $sum: 1 },
    }
  });

  // Format output
  pipeline.push({
    $project: {
      _id: 0,
      q1Avg: { $round: ['$q1Avg', 2] },
      q2Avg: { $round: ['$q2Avg', 2] },
      q3Avg: { $round: ['$q3Avg', 2] },
      q4Avg: { $round: ['$q4Avg', 2] },
      q5Avg: { $round: ['$q5Avg', 2] },
      npsAvg: { $round: ['$npsAvg', 2] },
      responseCount: 1,
    }
  });

  const result = await ResponseModel.aggregate(pipeline);
  
  if (result.length === 0) {
    return {
      q1Avg: 0,
      q2Avg: 0,
      q3Avg: 0,
      q4Avg: 0,
      q5Avg: 0,
      npsAvg: 0,
      responseCount: 0,
    };
  }

  return result[0];
} 