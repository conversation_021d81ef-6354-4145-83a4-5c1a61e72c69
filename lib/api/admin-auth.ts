'use server';

import dbConnect from '../mongodb';
import { AdminModel, IAdminDoc } from '../models/admin';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET as string;
const COOKIE_NAME = 'admin-session';

export interface IAdminAuthResult {
  success: boolean;
  admin?: {
    id: string;
    email: string;
    name: string;
    role: 'admin' | 'superadmin';
  };
  error?: string;
}

export async function authenticateAdmin(
  email: string,
  password: string
): Promise<IAdminAuthResult> {
  try {
    await dbConnect();

    // Find admin by email
    const admin = await AdminModel.findOne({
      email: email.toLowerCase().trim(),
      isActive: true,
    });

    if (!admin) {
      return {
        success: false,
        error: 'Invalid email or password',
      };
    }

    // Check password
    const isPasswordValid = await admin.comparePassword(password);
    if (!isPasswordValid) {
      return {
        success: false,
        error: 'Invalid email or password',
      };
    }

    // Update last login
    await AdminModel.findByIdAndUpdate(admin._id, {
      lastLoginAt: new Date(),
    });

    // Create JWT token
    const token = jwt.sign(
      {
        id: admin._id.toString(),
        email: admin.email,
        role: admin.role,
      },
      JWT_SECRET,
      { expiresIn: '8h' }
    );

    // Set HTTP-only cookie
    const cookieStore = await cookies();
    cookieStore.set(COOKIE_NAME, token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 8 * 60 * 60, // 8 hours
      path: '/',
    });

    return {
      success: true,
      admin: {
        id: admin._id.toString(),
        email: admin.email,
        name: admin.name,
        role: admin.role,
      },
    };
  } catch (error) {
    console.error('Admin authentication error:', error);
    return {
      success: false,
      error: 'Authentication failed',
    };
  }
}

export async function getAdminSession(): Promise<IAdminAuthResult> {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get(COOKIE_NAME)?.value;

    if (!token) {
      return {
        success: false,
        error: 'No session found',
      };
    }

    // Verify JWT token
    const decoded = jwt.verify(token, JWT_SECRET) as {
      id: string;
      email: string;
      role: 'admin' | 'superadmin';
    };

    await dbConnect();

    // Verify admin still exists and is active
    const admin = await AdminModel.findById(decoded.id);
    if (!admin || !admin.isActive) {
      return {
        success: false,
        error: 'Session invalid',
      };
    }

    return {
      success: true,
      admin: {
        id: admin._id.toString(),
        email: admin.email,
        name: admin.name,
        role: admin.role,
      },
    };
  } catch {
    return {
      success: false,
      error: 'Invalid session',
    };
  }
}

export async function logoutAdmin(): Promise<void> {
  const cookieStore = await cookies();
  cookieStore.delete(COOKIE_NAME);
}

export async function requireAdminAuth(): Promise<IAdminDoc> {
  const session = await getAdminSession();

  if (!session.success || !session.admin) {
    redirect('/admin/login');
  }

  await dbConnect();
  const admin = await AdminModel.findById(session.admin.id);

  if (!admin || !admin.isActive) {
    redirect('/admin/login');
  }

  return admin;
}

export async function requireSuperAdminAuth(): Promise<IAdminDoc> {
  const admin = await requireAdminAuth();

  if (admin.role !== 'superadmin') {
    throw new Error('Superadmin access required');
  }

  return admin;
}

export async function resetAdminPassword(
  adminId: string,
  newPassword: string,
  requestingAdminId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    await dbConnect();

    // Verify requesting admin is a superadmin
    const requestingAdmin = await AdminModel.findById(requestingAdminId);
    if (!requestingAdmin || requestingAdmin.role !== 'superadmin') {
      return {
        success: false,
        error: 'Unauthorized: Superadmin access required',
      };
    }

    // Find target admin
    const targetAdmin = await AdminModel.findById(adminId);
    if (!targetAdmin) {
      return {
        success: false,
        error: 'Admin not found',
      };
    }

    // Update password (will be hashed by pre-save hook)
    targetAdmin.password = newPassword;
    await targetAdmin.save();

    return { success: true };
  } catch (error) {
    console.error('Password reset error:', error);
    return {
      success: false,
      error: 'Password reset failed',
    };
  }
}

export async function createAdmin(
  email: string,
  password: string,
  name: string,
  role: 'admin' | 'superadmin',
  requestingAdminId: string
): Promise<{
  success: boolean;
  admin?: {
    id: string;
    email: string;
    name: string;
    role: 'admin' | 'superadmin';
    isActive: boolean;
    createdAt: Date;
  };
  error?: string;
}> {
  try {
    await dbConnect();

    // Verify requesting admin is a superadmin
    const requestingAdmin = await AdminModel.findById(requestingAdminId);
    if (!requestingAdmin || requestingAdmin.role !== 'superadmin') {
      return {
        success: false,
        error: 'Unauthorized: Superadmin access required',
      };
    }

    // Check if admin with this email already exists
    const existingAdmin = await AdminModel.findOne({
      email: email.toLowerCase().trim(),
    });
    if (existingAdmin) {
      return {
        success: false,
        error: 'Admin with this email already exists',
      };
    }

    // Create new admin
    const newAdmin = new AdminModel({
      email: email.toLowerCase().trim(),
      password,
      name: name.trim(),
      role,
      isActive: true,
    });

    await newAdmin.save();

    return {
      success: true,
      admin: {
        id: newAdmin._id.toString(),
        email: newAdmin.email,
        name: newAdmin.name,
        role: newAdmin.role,
        isActive: newAdmin.isActive,
        createdAt: newAdmin.createdAt,
      },
    };
  } catch (error) {
    console.error('Create admin error:', error);
    return {
      success: false,
      error: 'Failed to create admin account',
    };
  }
}

export async function getAllAdmins(requestingAdminId: string): Promise<{
  success: boolean;
  admins?: Array<{
    id: string;
    email: string;
    name: string;
    role: 'admin' | 'superadmin';
    isActive: boolean;
    lastLoginAt?: Date;
    createdAt: Date;
  }>;
  error?: string;
}> {
  try {
    await dbConnect();

    // Verify requesting admin is a superadmin
    const requestingAdmin = await AdminModel.findById(requestingAdminId);
    if (!requestingAdmin || requestingAdmin.role !== 'superadmin') {
      return {
        success: false,
        error: 'Unauthorized: Superadmin access required',
      };
    }

    // Get all admins
    const admins = await AdminModel.find(
      {},
      {
        password: 0, // Exclude password field
      }
    ).sort({ createdAt: -1 });

    return {
      success: true,
      admins: admins.map((admin: IAdminDoc) => ({
        id: admin._id.toString(),
        email: admin.email,
        name: admin.name,
        role: admin.role,
        isActive: admin.isActive,
        lastLoginAt: admin.lastLoginAt,
        createdAt: admin.createdAt,
      })),
    };
  } catch (error) {
    console.error('Get admins error:', error);
    return {
      success: false,
      error: 'Failed to retrieve admins',
    };
  }
}
