import { Document, Schema, model, models, Types } from 'mongoose';

export interface IUserPasscodeInput {
  userEmail: string;
  passcode: string;
  waveId: Types.ObjectId;
  expiresAt?: Date;
  isActive?: boolean;
}

export interface IUserPasscodeDoc extends IUserPasscodeInput, Document {
  _id: Types.ObjectId;
  isActive: boolean;
  usedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const UserPasscodeSchema = new Schema<IUserPasscodeDoc>(
  {
    userEmail: { type: String, required: true },
    passcode: { type: String, required: true },
    waveId: { type: Schema.Types.ObjectId, ref: 'Wave', required: true },
    expiresAt: { type: Date },
    isActive: { type: Boolean, default: true },
    usedAt: { type: Date },
  },
  { timestamps: true }
);

// Ensure one active passcode per user per wave
UserPasscodeSchema.index({ userEmail: 1, waveId: 1 }, { unique: true });

// Ensure passcodes are globally unique (no two users can have the same passcode)
UserPasscodeSchema.index({ passcode: 1 }, { unique: true });

export const UserPasscodeModel = 
  models.UserPasscode || model<IUserPasscodeDoc>('UserPasscode', UserPasscodeSchema); 