import { Document, Schema, model, models, Types } from 'mongoose';
import bcrypt from 'bcryptjs';

export interface IAdminInput {
  email: string;
  password: string;
  name: string;
  role: 'admin' | 'superadmin';
  isActive?: boolean;
  lastLoginAt?: Date;
}

export interface IAdminDoc extends IAdminInput, Document {
  _id: Types.ObjectId;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
  comparePassword(password: string): Promise<boolean>;
}

const AdminSchema = new Schema<IAdminDoc>(
  {
    email: { 
      type: String, 
      required: true, 
      unique: true,
      lowercase: true,
      trim: true 
    },
    password: { 
      type: String, 
      required: true,
      minlength: 8 
    },
    name: { 
      type: String, 
      required: true,
      trim: true 
    },
    role: { 
      type: String, 
      enum: ['admin', 'superadmin'], 
      required: true,
      default: 'admin' 
    },
    isActive: { 
      type: Boolean, 
      default: true 
    },
    lastLoginAt: { 
      type: Date 
    },
  },
  { timestamps: true }
);

// Hash password before saving
AdminSchema.pre('save', async function (next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Compare password method
AdminSchema.methods.comparePassword = async function (password: string): Promise<boolean> {
  return bcrypt.compare(password, this.password);
};

export const AdminModel = 
  models.Admin || model<IAdminDoc>('Admin', AdminSchema);