import { Document, Schema, model, models, Types } from 'mongoose';

export interface IWaveConfig {
  selectedRegions: string[];
  selectedAgencyTypes: string[];
  selectedBrands: string[];
  selectedAgencies: string[];
  selectedPeriod: string;
  selectedYear: number;
}

export interface IWaveInput {
  name: string;
  status?: 'draft' | 'launched' | 'active' | 'completed' | 'archived' | 'seed';
  config?: IWaveConfig;
}

export interface IWaveDoc extends IWaveInput, Document {
  _id: Types.ObjectId;
  status: 'draft' | 'launched' | 'active' | 'completed' | 'archived' | 'seed';
  config?: IWaveConfig;
  createdAt: Date;
  updatedAt: Date;
}

const WaveSchema = new Schema<IWaveDoc>(
  {
    name: { type: String, required: true, unique: true },
    status: { 
      type: String, 
      enum: ['draft', 'launched', 'active', 'completed', 'archived', 'seed'],
      default: 'draft'
    },
    config: {
      type: Schema.Types.Mixed,
      required: false
    },
  },
  { timestamps: true }
);

// Clear any existing model to force schema update
if (models.Wave) {
  delete models.Wave;
}

export const WaveModel = model<IWaveDoc>('Wave', WaveSchema);
