import { Document, Schema, model, models, Types } from 'mongoose';

export interface ISurveyImport {
  accountName: string;
  agencyName: string;
  agencyType: string;
  brand: string;
  country: string;
  region: string;
  assessmentType: string;
  userName: string;
  userEmail?: string;
  userStatus: string;
  inScope: string;
  notes?: string;
}

export interface ISurveyInput extends ISurveyImport {
  waveId: Types.ObjectId;
}

export interface ISurveyDoc extends ISurveyInput, Document {
  _id: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const SurveySchema = new Schema<ISurveyDoc>(
  {
    waveId: { type: Schema.Types.ObjectId, ref: 'Wave', required: true },
    accountName: { type: String, required: true },
    agencyName: { type: String, required: true },
    agencyType: { type: String, required: true },
    brand: { type: String, required: true },
    country: { type: String, required: true },
    region: { type: String, required: true },
    assessmentType: { type: String, required: true },
    userName: { type: String, required: true },
    userEmail: { type: String, default: '' },
    userStatus: { type: String, required: true },
    inScope: { type: String, required: true },
    notes: { type: String, default: '' },
  },
  { timestamps: true }
);

// Index for finding surveys by user email and wave
SurveySchema.index({ userEmail: 1, waveId: 1 });

export const SurveyModel = models.Survey || model<ISurveyDoc>('Survey', SurveySchema);
