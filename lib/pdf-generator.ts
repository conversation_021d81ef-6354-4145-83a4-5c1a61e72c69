import PDFDocument from 'pdfkit';
import { getWaveStatistics } from './api/waves';
import { getSurveysByWave } from './api/surveys';
import { ISurveyDoc } from './models/survey';
import { ResponseModel, IResponseDoc } from './models/response';
import { WaveModel } from './models/wave';
import dbConnect from './mongodb';

interface DashboardFilters {
  waves: string[];
  agency: string;
  brand: string;
  region: string;
}

// Helper function to convert wave names to wave IDs
async function getWaveIdByName(waveName: string): Promise<string | null> {
  await dbConnect();
  const wave = await WaveModel.findOne({ name: waveName });
  return wave ? wave._id.toString() : null;
}

export async function generateDashboardPDF(
  filters: DashboardFilters,
  filename: string
): Promise<Buffer> {
  return new Promise(async (resolve, reject) => {
    try {
      const doc = new PDFDocument({
        margin: 50,
        font: 'Helvetica' // Use built-in font to avoid font file issues
      });
      const chunks: Buffer[] = [];

      doc.on('data', (chunk) => chunks.push(chunk));
      doc.on('end', () => resolve(Buffer.concat(chunks)));

      // Title Page
      doc.fontSize(24).text('Echo360 Dashboard Report', { align: 'center' });
      doc.moveDown();

      // Add timestamp
      const timestamp = new Date().toLocaleString();
      doc.fontSize(12).text(`Generated: ${timestamp}`, { align: 'center' });
      doc.moveDown();

      // Filter Information
      doc.fontSize(16).text('Applied Filters:', { underline: true });
      doc.fontSize(12);
      doc.text(`Waves: ${filters.waves.length > 0 ? filters.waves.join(', ') : 'All Waves'}`);
      doc.text(`Agency: ${filters.agency}`);
      doc.text(`Brand: ${filters.brand}`);
      doc.text(`Region: ${filters.region}`);
      doc.moveDown(2);

      // Convert wave names to wave IDs
      const waveIds: string[] = [];
      for (const waveName of filters.waves) {
        const waveId = await getWaveIdByName(waveName);
        if (waveId) {
          waveIds.push(waveId);
        } else {
          console.error(`Wave not found: ${waveName}`);
        }
      }

      // Fetch data based on filters
      let allSurveys: ISurveyDoc[] = [];
      if (waveIds.length > 0) {
        for (const waveId of waveIds) {
          const surveys = await getSurveysByWave(waveId);
          allSurveys = allSurveys.concat(surveys);
        }
      }

      // Apply additional filters
      const filteredSurveys = allSurveys.filter(survey => {
        return (
          (filters.agency === 'All Agencies' || survey.agencyName === filters.agency) &&
          (filters.brand === 'All Brands' || survey.brand === filters.brand) &&
          (filters.region === 'All Regions' || survey.region === filters.region)
        );
      });

      // Statistics Panel
      doc.addPage();
      doc.fontSize(18).text('Dashboard Statistics', { underline: true });
      doc.moveDown();

      // Get responses for the filtered surveys
      const surveyIds = filteredSurveys.map(s => s._id);
      const allResponses: IResponseDoc[] = await ResponseModel.find({
        surveyId: { $in: surveyIds }
      });

      const totalSurveys = filteredSurveys.length;
      const completedSurveys = allResponses.length;
      const completionRate = totalSurveys > 0 ? (completedSurveys / totalSurveys * 100).toFixed(1) : '0';

      doc.fontSize(14);
      doc.text(`Total Surveys: ${totalSurveys}`);
      doc.text(`Completed Surveys: ${completedSurveys}`);
      doc.text(`Completion Rate: ${completionRate}%`);
      doc.moveDown();

      // Calculate average scores
      if (allResponses.length > 0) {
        const avgScore = allResponses.reduce((sum, r) => sum + (r.overallRating || 0), 0) / allResponses.length;
        doc.text(`Average Score: ${avgScore.toFixed(1)}`);
      }

      // Bar Chart Data Panel
      doc.addPage();
      doc.fontSize(18).text('Survey Response Data', { underline: true });
      doc.moveDown();

      // Group by agency for chart representation
      const agencyData = filteredSurveys.reduce((acc, survey) => {
        const agency = survey.agencyName || 'Unknown';
        if (!acc[agency]) {
          acc[agency] = { total: 0, completed: 0 };
        }
        acc[agency].total++;
        // Check if this survey has responses
        const hasResponse = allResponses.some(r => r.surveyId.toString() === survey._id.toString());
        if (hasResponse) {
          acc[agency].completed++;
        }
        return acc;
      }, {} as Record<string, { total: number; completed: number }>);

      doc.fontSize(14);
      Object.entries(agencyData).forEach(([agency, data]) => {
        const rate = data.total > 0 ? (data.completed / data.total * 100).toFixed(1) : '0';
        doc.text(`${agency}: ${data.completed}/${data.total} (${rate}%)`);
      });

      // Comments Panel
      const comments: string[] = [];
      allResponses.forEach(response => {
        for (let i = 1; i <= 5; i++) {
          const comment = response[`q${i}Comment` as keyof IResponseDoc] as string;
          if (comment && comment.trim()) {
            comments.push(comment.trim());
          }
        }
      });

      if (comments.length > 0) {
        doc.addPage();
        doc.fontSize(18).text('Survey Comments', { underline: true });
        doc.moveDown();

        doc.fontSize(12);
        comments.slice(0, 20).forEach((comment, index) => {
          doc.text(`${index + 1}. ${comment}`);
          doc.moveDown(0.5);
        });
      }

      // Wave History Panel
      if (filters.waves.length > 0) {
        doc.addPage();
        doc.fontSize(18).text('Wave History', { underline: true });
        doc.moveDown();

        for (const waveId of filters.waves) {
          try {
            const waveStats = await getWaveStatistics(waveId);
            doc.fontSize(14).text(`Wave: ${waveStats.waveName || waveId}`, { underline: true });
            doc.fontSize(12);
            doc.text(`Status: ${waveStats.status || 'Unknown'}`);
            doc.text(`Total Surveys: ${waveStats.totalSurveys || 0}`);
            doc.text(`Completed: ${waveStats.completedSurveys || 0}`);
            if (waveStats.averageScore) {
              doc.text(`Average Score: ${waveStats.averageScore.toFixed(1)}`);
            }
            doc.moveDown();
          } catch (error) {
            console.error(`Error fetching wave stats for ${waveId}:`, error);
          }
        }
      }

      // Footer
      doc.fontSize(10).text(
        `Echo360 Dashboard Report - ${filename}`,
        50,
        doc.page.height - 50,
        { align: 'center' }
      );

      doc.end();
    } catch (error) {
      reject(error);
    }
  });
}
