# Wave Management Guide

This document explains how wave management works in the Echo system, particularly the differences between seed waves and regular assessment waves.

## Wave Types

### Seed Waves

- **Purpose**: Template data for creating new assessment waves
- **Status**: `seed`
- **Content**: Survey templates with agency, brand, region, and assessment type data
- **Responses**: No response data collected
- **Use Case**: Used as the foundation for launching new assessment waves

### Regular Waves

- **Purpose**: Active or completed assessment campaigns
- **Status**: `draft`, `active`, `completed`, `archived`
- **Content**: Surveys with associated response data
- **Responses**: Contains actual survey responses and performance metrics
- **Use Case**: Track assessment progress and analyze results

## Features by Wave Type

### Seed Wave View

When viewing a seed wave, you'll see:

- **Seed Data Overview**: Explanation that this is template data
- **Available Data**: Counts of agencies, brands, regions, assessment types, and agency types
- **Agency Seed Data Details**: Survey counts per agency (no response metrics)
- **Complete Lists**: Full lists of all available entities for launching new waves

**Hidden for Seed Waves**:

- Response statistics (total responses, completion rate, scores, NPS)
- Agency performance details with response metrics

### Regular Wave View

When viewing a regular wave, you'll see:

- **Summary Statistics**: Total responses, completion rate, average scores, NPS
- **Participating Entities**: Counts of active participants
- **Agency Performance Details**: Response counts and performance metrics per agency
- **Complete Lists**: Lists of participating entities

## Launch Wave Feature

The "Launch Wave" functionality uses the latest seed wave data to:

1. Fetch available agencies, brands, regions, and agency types
2. Allow selection/filtering of entities for the new wave
3. Create survey templates based on selections
4. Generate a new wave ready for activation

## API Endpoints

### Seed Data

- `GET /api/launch-wave/seed-data` - Fetch data from latest seed wave
- `GET /api/waves/seed` - List all seed waves

### Wave Statistics

- `GET /api/waves/[waveId]/stats` - Get wave statistics (handles both seed and regular waves)

## Navigation

- **Waves Management**: `/admin/waves` - View and manage all waves
- **Launch New Wave**: `/admin/launch-wave` - Create new waves from seed data
- **Import Seed Data**: `/admin/import-surveys` - Import survey templates as seed data
