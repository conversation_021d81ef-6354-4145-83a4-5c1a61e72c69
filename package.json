{"name": "echo360", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "test": "NODE_ENV=test next build", "lint": "next lint", "seed-admins": "npx tsx util/seed-admins.ts", "test-auth": "npx tsx util/test-auth-system.ts", "test-email": "npx tsx util/test-email.ts"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-charts": "^8.4.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/pdfkit": "^0.14.0", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "mailgun.js": "^12.0.1", "mongoose": "^8.14.1", "next": "15.3.1", "papaparse": "^5.5.2", "pdfkit": "^0.17.1", "postcss-preset-env": "^10.2.0", "pptxgenjs": "^4.0.1", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/mongoose": "^5.11.96", "@types/node": "^20.19.9", "@types/papaparse": "^5.3.16", "@types/react": "^19", "@types/react-dom": "^19", "dotenv-flow": "^4.1.0", "eslint": "^9", "eslint-config-next": "15.3.1", "tsx": "^4.20.3", "typescript": "^5"}}