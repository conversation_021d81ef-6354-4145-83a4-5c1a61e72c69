'use client';

import * as React from 'react';
import { useServerInsertedHTML } from 'next/navigation';
import createEmotionServer from '@emotion/server/create-instance';
import createEmotionCache from './emotionCache';
import { CacheProvider } from '@emotion/react';

export default function MuiRegistry({
  children,
}: {
  children: React.ReactNode;
}) {
  const [cache] = React.useState(() => createEmotionCache());
  const { extractCriticalToChunks } = createEmotionServer(cache);

  useServerInsertedHTML(() => {
    const chunks = extractCriticalToChunks('');
    return (
      <style
        data-emotion={`css ${chunks.styles
          .map((style: { key: string }) => style.key)
          .join(' ')}`}
        dangerouslySetInnerHTML={{
          __html: chunks.styles
            .map((style: { css: string }) => style.css)
            .join(''),
        }}
      />
    );
  });

  return <CacheProvider value={cache}>{children}</CacheProvider>;
}
