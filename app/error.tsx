'use client';

import { startTransition } from 'react';
import { useRouter } from 'next/navigation';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const router = useRouter();

  const retryButtonOnClick = () => {
    startTransition(() => {
      reset();
      router.refresh();
    });
  };

  return (
    <div className="grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 font-[family-name:var(--font-geist-sans)]">
      <main className="flex flex-col gap-[32px] row-start-2 items-center">
        <div className="flex flex-col items-center">
          <h2>Something went wrong!</h2>
          <h3>{error.message}</h3>
          <button
            className="bg-secondary text-white p-2 rounded"
            onClick={retryButtonOnClick}
          >
            Retry
          </button>
        </div>
      </main>
    </div>
  );
}
