'use client';

import { Box, styled } from '@mui/material';

export interface ContainerProps {
  direction?: 'row' | 'column';
  gap?: number;
  justify?:
    | 'flex-start'
    | 'center'
    | 'flex-end'
    | 'space-between'
    | 'space-around';
  align?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
  fullWidth?: boolean;
  fullHeight?: boolean;
  padding?: number | { x?: number; y?: number };
}

const shouldForwardProp = (prop: string) =>
  ![
    'direction',
    'gap',
    'justify',
    'align',
    'fullWidth',
    'fullHeight',
    'padding',
  ].includes(prop);

export const CenteredContainer = styled(Box, {
  shouldForwardProp,
})<ContainerProps>(
  ({
    theme,
    direction = 'column',
    gap = 0,
    justify = 'center',
    align = 'center',
    fullWidth,
    fullHeight,
    padding,
  }) => ({
    display: 'flex',
    flexDirection: direction,
    alignItems: align,
    justifyContent: justify,
    gap: theme.spacing(gap),
    width: fullWidth ? '100%' : 'auto',
    height: fullHeight ? '100%' : 'auto',
    padding: padding
      ? typeof padding === 'number'
        ? theme.spacing(padding)
        : `${theme.spacing(padding.y || 0)} ${theme.spacing(padding.x || 0)}`
      : undefined,
  })
);

// Specific container variants
export const LogoContainer = styled(CenteredContainer)(({ theme }) => ({
  // Add any logo-specific styles here
  gap: theme.spacing(2),
}));

export const PageContainer = styled(CenteredContainer)(({ theme }) => ({
  minHeight: '100vh',
  padding: theme.spacing(2),
  [theme.breakpoints.up('sm')]: {
    padding: theme.spacing(2.5),
  },
}));

export const MainContent = styled(CenteredContainer)(({ theme }) => ({
  gap: theme.spacing(4),
  gridRow: 2,
}));
