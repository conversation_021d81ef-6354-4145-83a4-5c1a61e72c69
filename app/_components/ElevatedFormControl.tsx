'use client';

import { FormControl, FormControlProps } from '@mui/material';
import { styled } from '@mui/material/styles';

// Styled FormControl with elevated shadow effect
const ElevatedFormControl = styled(FormControl)(({ theme }) => ({
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.15)',
    borderRadius: theme.spacing(1),
    zIndex: 0,
    transform: 'translate(4px, 4px)',
  },
  '&:hover::after': {
    backgroundColor: 'rgba(0,0,0,0.2)',
    transform: 'translate(5px, 5px)',
  },
  '&:focus-within::after': {
    backgroundColor: 'rgba(25,118,210,0.15)',
    transform: 'translate(5px, 5px)',
  },
  '& .MuiInputLabel-root': {
    zIndex: 2,
    backgroundColor: theme.palette.background.paper,
    padding: '0 4px',
  },
  '& .MuiInputBase-root, & .MuiToggleButtonGroup-root': {
    backgroundColor: theme.palette.background.paper,
  },
  '& .MuiButton-root[disabled]': {
    backgroundColor: theme.palette.background.paper,
  },
  '& .MuiInputBase-root, & .MuiButton-root': {
    borderRadius: theme.spacing(1),
    border: '1px solid',
    borderColor: theme.palette.divider,
  },
  '& .MuiInputBase-root, & .MuiButtonBase-root, & .MuiToggleButtonGroup-root': {
    position: 'relative',
    zIndex: 1,
    '&:hover': {
      borderColor: theme.palette.primary.main,
    },
    '&.Mui-focused': {
      borderColor: theme.palette.primary.main,
    },
    '& fieldset': {
      border: 'none',
    },
  },
  '& .MuiButtonBase-root': {
    // borderRadius: theme.spacing(1),
    // border: '1px solid',
    // borderColor: theme.palette.divider,
    // position: 'relative',
    // zIndex: 1,
    // '&:hover': {
    //   borderColor: theme.palette.primary.main,
    // },
    // '&.Mui-focused': {
    //   borderColor: theme.palette.primary.main,
    // },
    // '& fieldset': {
    //   border: 'none',
    // },
  },
}));

// Export the component with proper TypeScript typing
export default function ElevatedFormControlComponent(props: FormControlProps) {
  return <ElevatedFormControl {...props} />;
}

// Also export the styled component directly for advanced use cases
export { ElevatedFormControl };
