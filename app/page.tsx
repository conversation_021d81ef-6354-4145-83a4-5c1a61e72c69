import BrandLogo from './_components/brand-logo.svg';
import { Typography } from '@mui/material';
import {
  PageContainer,
  MainContent,
  LogoContainer,
} from './_components/containers';
import { BrandText } from './_components/brand-text';

export default function Home() {
  return (
    <PageContainer
      direction="column"
      fullWidth
      fullHeight
      padding={{ x: 2, y: 2.5 }}
    >
      <MainContent as="main" direction="column" gap={4}>
        <LogoContainer>
          <BrandLogo fill="fill-primary" width={'180pt'} height={'180pt'} />
          <BrandText variant="body1">
            <Typography component="span" variant="h5" className="echo">
              echo
            </Typography>
            <Typography component="span" variant="h5" className="number">
              360
            </Typography>
          </BrandText>
        </LogoContainer>
      </MainContent>
    </PageContainer>
  );
}
