'use client';

import { Box } from '@mui/material';
import { useState, useEffect, useRef, ReactElement, cloneElement } from 'react';

interface SyncedHeightPanelsProps {
  leftPanel: ReactElement;
  rightPanel: ReactElement;
  showRightPanel?: boolean;
  gap?: number;
}

export default function SyncedHeightPanels({
  leftPanel,
  rightPanel,
  showRightPanel = true,
  gap = 3,
}: SyncedHeightPanelsProps) {
  const [syncedHeight, setSyncedHeight] = useState<number | undefined>(
    undefined
  );
  const leftPanelRef = useRef<HTMLDivElement>(null);
  const rightPanelRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const leftElement = leftPanelRef.current;
    const rightElement = rightPanelRef.current;

    if (!leftElement || (!rightElement && showRightPanel)) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.target === leftElement) {
          const leftHeight = entry.contentRect.height;
          setSyncedHeight(leftHeight);
        }
      }
    });

    // Observe the left panel (chart) for height changes
    resizeObserver.observe(leftElement);

    // Initial height calculation
    const updateHeight = () => {
      const leftHeight = leftElement.getBoundingClientRect().height;
      setSyncedHeight(leftHeight);
    };

    // Update height after a brief delay to allow for initial render
    const timeoutId = setTimeout(updateHeight, 100);

    return () => {
      resizeObserver.disconnect();
      clearTimeout(timeoutId);
    };
  }, [showRightPanel]);

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'flex-start',
        gap: showRightPanel ? gap : 0,
      }}
    >
      <Box ref={leftPanelRef} sx={{ flex: showRightPanel ? 1 : '1 1 100%' }}>
        {leftPanel}
      </Box>
      {showRightPanel && (
        <Box ref={rightPanelRef} sx={{ flex: 1 }}>
          {cloneElement(rightPanel, {
            height: syncedHeight,
          } as { height?: number })}
        </Box>
      )}
    </Box>
  );
}
