'use client';

import { Typography, TypographyProps } from '@mui/material';
import { ReactNode } from 'react';

interface PanelHeaderProps
  extends Omit<TypographyProps, 'variant' | 'children'> {
  children: ReactNode;
}

export default function PanelHeader({
  children,
  sx,
  ...props
}: PanelHeaderProps) {
  return (
    <Typography
      variant="h6"
      color="text.primary"
      sx={{
        fontSize: '1.1rem',
        ...sx,
      }}
      {...props}
    >
      {children}
    </Typography>
  );
}
