'use client';

import {
  Typo<PERSON>,
  Box,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableSortLabel,
  CircularProgress,
  useTheme,
} from '@mui/material';
import { useState, useEffect } from 'react';
import PanelHeader from './PanelHeader';
import DashboardPanel from './DashboardPanel';
import PanelSubheader from './PanelSubheader';

interface DrilldownData {
  name: string;
  averageScore: number;
  responseCount: number;
  npsScore: number;
  q1Avg: number;
  q2Avg: number;
  q3Avg: number;
  q4Avg: number;
  q5Avg: number;
}

interface DrilldownPanelProps {
  primaryFilter: 'agency' | 'brand' | 'region';
  primaryValue: string;
  wave?: string;
}

type Order = 'asc' | 'desc';

export default function DrilldownPanel({
  primaryFilter,
  primaryValue,
  wave,
}: DrilldownPanelProps) {
  const [orderBy, setOrderBy] = useState<string>('averageScore');
  const [order, setOrder] = useState<Order>('desc');
  const [drilldownData, setDrilldownData] = useState<{
    agencies?: DrilldownData[];
    brands?: DrilldownData[];
    regions?: DrilldownData[];
  }>({});
  const [loading, setLoading] = useState(true);
  const theme = useTheme();

  // Map dimension keys to display labels
  const dimensionLabels: Record<string, string> = {
    agencies: 'Agency',
    brands: 'Brand',
    regions: 'Region',
  };

  // Fetch drilldown data when filters change
  useEffect(() => {
    const fetchDrilldownData = async () => {
      try {
        setLoading(true);

        const params = new URLSearchParams();
        params.append('primaryFilter', primaryFilter);
        params.append('primaryValue', primaryValue);
        if (wave) {
          params.append('wave', wave);
        }

        const response = await fetch(
          `/api/responses/drilldown?${params.toString()}`
        );
        const data = await response.json();
        setDrilldownData(data.drilldownData || {});
      } catch (error) {
        console.error('Error fetching drilldown data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDrilldownData();
  }, [primaryFilter, primaryValue, wave]);

  const handleSort = (property: string) => {
    setOrderBy(property);
    setOrder(orderBy === property && order === 'asc' ? 'desc' : 'asc');
  };

  const sortData = (data: DrilldownData[]) => {
    return [...data].sort((a, b) => {
      if (orderBy === 'name') {
        return order === 'asc'
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      } else {
        const aValue = (a[orderBy as keyof DrilldownData] as number) || 0;
        const bValue = (b[orderBy as keyof DrilldownData] as number) || 0;
        return order === 'asc' ? aValue - bValue : bValue - aValue;
      }
    });
  };

  if (loading) {
    return (
      <DashboardPanel
        sx={{
          p: 2,
          height: 400,
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <CircularProgress />
      </DashboardPanel>
    );
  }

  const dimensions = Object.keys(drilldownData);

  if (dimensions.length === 0) {
    return (
      <DashboardPanel sx={{ p: 2 }}>
        <PanelHeader gutterBottom>Drilldown Analysis</PanelHeader>
        <Typography color="text.secondary">
          No data available for the selected filter
        </Typography>
      </DashboardPanel>
    );
  }

  return (
    <DashboardPanel
      sx={{ p: 2, overflowY: 'auto', overflowX: 'hidden', gap: 3 }}
    >
      <Box>
        <PanelHeader gutterBottom>
          Drilldown{' '}
          {primaryFilter.charAt(0).toUpperCase() + primaryFilter.slice(1)}:{' '}
          {primaryValue}
        </PanelHeader>
      </Box>

      {dimensions.map((dimension) => {
        const data =
          drilldownData[dimension as keyof typeof drilldownData] || [];
        const sortedData = sortData(data);
        const dimensionLabel = dimensionLabels[dimension] || dimension;

        return (
          <Box key={dimension} sx={{ mb: 3, width: '100%' }}>
            <PanelSubheader
              sx={{
                textAlign: 'center',
                mb: 1,
              }}
            >
              {dimensionLabel} Performance
            </PanelSubheader>
            <TableContainer sx={{ width: '100%', overflowX: 'hidden' }}>
              <Table
                size="small"
                stickyHeader
                sx={{
                  borderTop: `1px solid ${theme.palette.divider}`,
                  width: '100%',
                  tableLayout: 'fixed',
                  '& .MuiTableCell-root': {
                    wordWrap: 'break-word',
                    whiteSpace: 'normal',
                    overflow: 'hidden',
                  },
                }}
              >
                <TableHead>
                  <TableRow
                    sx={{
                      backgroundColor: theme.palette.action.hover,
                      '& .MuiTableCell-head': {
                        fontWeight: 600,
                        backgroundColor: theme.palette.action.hover,
                      },
                    }}
                  >
                    <TableCell>
                      <TableSortLabel
                        active={orderBy === 'name'}
                        direction={orderBy === 'name' ? order : 'asc'}
                        onClick={() => handleSort('name')}
                      >
                        {dimensionLabel}
                      </TableSortLabel>
                    </TableCell>
                    <TableCell>
                      <TableSortLabel
                        active={orderBy === 'averageScore'}
                        direction={orderBy === 'averageScore' ? order : 'asc'}
                        onClick={() => handleSort('averageScore')}
                      >
                        Avg Score
                      </TableSortLabel>
                    </TableCell>
                    <TableCell>
                      <TableSortLabel
                        active={orderBy === 'npsScore'}
                        direction={orderBy === 'npsScore' ? order : 'asc'}
                        onClick={() => handleSort('npsScore')}
                      >
                        NPS
                      </TableSortLabel>
                    </TableCell>
                    <TableCell>
                      <TableSortLabel
                        active={orderBy === 'responseCount'}
                        direction={orderBy === 'responseCount' ? order : 'asc'}
                        onClick={() => handleSort('responseCount')}
                      >
                        Responses
                      </TableSortLabel>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {sortedData.map((row, index) => (
                    <TableRow
                      key={row.name}
                      sx={{
                        backgroundColor:
                          index % 2 === 0
                            ? theme.palette.background.paper
                            : theme.palette.action.hover,
                        '&:hover': {
                          backgroundColor: theme.palette.action.selected,
                        },
                      }}
                    >
                      <TableCell>{row.name}</TableCell>
                      <TableCell>
                        {row.averageScore?.toFixed(1) || '-'}
                      </TableCell>
                      <TableCell>{row.npsScore?.toFixed(1) || '-'}</TableCell>
                      <TableCell>{row.responseCount}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        );
      })}
    </DashboardPanel>
  );
}
