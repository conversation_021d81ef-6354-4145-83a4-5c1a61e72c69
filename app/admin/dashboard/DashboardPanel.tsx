'use client';

import { Paper, PaperProps, SxProps, Theme } from '@mui/material';
import { ReactNode } from 'react';

interface DashboardPanelProps extends Omit<PaperProps, 'children'> {
  children: ReactNode;
  sx?: SxProps<Theme>;
}

export default function DashboardPanel({
  children,
  sx,
  ...paperProps
}: DashboardPanelProps) {
  return (
    <Paper
      variant="outlined"
      sx={{
        p: 2,
        position: 'relative',
        borderRadius: 2,
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        // height: 'auto',
        minHeight: 0,
        ...sx,
      }}
      {...paperProps}
    >
      {children}
    </Paper>
  );
}
