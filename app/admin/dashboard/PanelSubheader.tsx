'use client';

import { Typography, TypographyProps } from '@mui/material';
import { ReactNode } from 'react';

interface PanelSubheaderProps
  extends Omit<TypographyProps, 'variant' | 'children'> {
  children: ReactNode;
}

export default function PanelSubheader({
  children,
  sx,
  ...props
}: PanelSubheaderProps) {
  return (
    <Typography
      variant="body2"
      color="text.secondary"
      sx={{
        fontSize: '0.75rem',
        ...sx,
      }}
      {...props}
    >
      {children}
    </Typography>
  );
}
