'use client';

import {
  Paper,
  Typography,
  Box,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableSortLabel,
  CircularProgress,
} from '@mui/material';
import { useState, useEffect } from 'react';
import { IResponseAnalytics } from '../../../lib/api/responses';

interface FilterOptions {
  brands: string[];
  regions: string[];
  waves: string[];
}

interface LeaderboardTableProps {
  selectedAgency: string;
  selectedBrand: string;
  selectedRegion: string;
  selectedWave: string;
  filterOptions: FilterOptions;
}

type Order = 'asc' | 'desc';

export default function LeaderboardTable({
  selectedAgency,
  selectedBrand,
  selectedRegion,
  selectedWave,
}: LeaderboardTableProps) {
  const [orderBy, setOrderBy] = useState<
    'agencyName' | 'averageScore' | 'npsScore'
  >('averageScore');
  const [order, setOrder] = useState<Order>('desc');
  const [analyticsData, setAnalyticsData] = useState<IResponseAnalytics[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch analytics data when filters change
  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);

        const params = new URLSearchParams();
        if (selectedAgency !== 'All Agencies')
          params.append('agencyName', selectedAgency);
        if (selectedBrand !== 'All Brands')
          params.append('brand', selectedBrand);
        if (selectedRegion !== 'All Regions')
          params.append('region', selectedRegion);

        const response = await fetch(
          `/api/responses/analytics?${params.toString()}`
        );
        const data = await response.json();
        setAnalyticsData(data);
      } catch (error) {
        console.error('Error fetching analytics data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [selectedAgency, selectedBrand, selectedRegion, selectedWave]);

  // Sorting
  const sorted = [...analyticsData].sort((a, b) => {
    if (orderBy === 'agencyName') {
      return order === 'asc'
        ? a.agencyName.localeCompare(b.agencyName)
        : b.agencyName.localeCompare(a.agencyName);
    } else {
      const aValue = a[orderBy] || 0;
      const bValue = b[orderBy] || 0;
      return order === 'asc' ? aValue - bValue : bValue - aValue;
    }
  });

  const handleSort = (property: 'agencyName' | 'averageScore' | 'npsScore') => {
    if (orderBy === property) {
      setOrder(order === 'asc' ? 'desc' : 'asc');
    } else {
      setOrderBy(property);
      setOrder('desc');
    }
  };

  return (
    <Paper sx={{ p: 2, bgcolor: '#e3f2fd' }}>
      <Typography variant="subtitle1" fontWeight={600} gutterBottom>
        Agency Leaderboard
      </Typography>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress size={24} />
        </Box>
      ) : (
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell
                  sortDirection={orderBy === 'agencyName' ? order : false}
                  sx={{ fontWeight: 700 }}
                >
                  <TableSortLabel
                    active={orderBy === 'agencyName'}
                    direction={orderBy === 'agencyName' ? order : 'asc'}
                    onClick={() => handleSort('agencyName')}
                  >
                    AGENCY
                  </TableSortLabel>
                </TableCell>
                <TableCell
                  sortDirection={orderBy === 'averageScore' ? order : false}
                  sx={{ fontWeight: 700 }}
                >
                  <TableSortLabel
                    active={orderBy === 'averageScore'}
                    direction={orderBy === 'averageScore' ? order : 'asc'}
                    onClick={() => handleSort('averageScore')}
                  >
                    AVG
                  </TableSortLabel>
                </TableCell>
                <TableCell
                  sortDirection={orderBy === 'npsScore' ? order : false}
                  sx={{ fontWeight: 700 }}
                >
                  <TableSortLabel
                    active={orderBy === 'npsScore'}
                    direction={orderBy === 'npsScore' ? order : 'asc'}
                    onClick={() => handleSort('npsScore')}
                  >
                    NPS
                  </TableSortLabel>
                </TableCell>
                <TableCell sx={{ fontWeight: 700 }}>RESPONSES</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {sorted.slice(0, 10).map((row, index) => (
                <TableRow key={`${row.agencyName}-${index}`}>
                  <TableCell>{row.agencyName}</TableCell>
                  <TableCell>{row.averageScore.toFixed(1)}</TableCell>
                  <TableCell>{row.npsScore?.toFixed(1) || 'N/A'}</TableCell>
                  <TableCell>{row.responseCount}</TableCell>
                </TableRow>
              ))}
              {sorted.length === 0 && (
                <TableRow>
                  <TableCell
                    colSpan={4}
                    align="center"
                    sx={{ py: 4, color: 'text.secondary' }}
                  >
                    No data available for the selected filters
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Paper>
  );
}
