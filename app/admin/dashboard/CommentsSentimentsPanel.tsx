'use client';

import {
  <PERSON>po<PERSON>,
  Box,
  CircularProgress,
  useTheme,
  IconButton,
  Tooltip,
  Snackbar,
  Alert,
} from '@mui/material';
import { useState, useEffect } from 'react';
import { ContentCopy as ContentCopyIcon } from '@mui/icons-material';
import DashboardPanel from './DashboardPanel';
import PanelHeader from './PanelHeader';

interface CommentsSentimentsPanelProps {
  focusType: 'agency' | 'brand' | 'region' | 'wave';
  focusValue: string;
  wave?: string; // Optional wave filter
  height?: number;
}

interface ParsedComment {
  assessorType: string;
  commentText: string;
}

const parseComment = (comment: string): ParsedComment | null => {
  // Expected format: "(Assessor Type): Comment text" or "(Agency Name): Comment text"
  const match = comment.match(/^\((.+?)\):\s*(.+)$/);
  if (match) {
    return {
      assessorType: match[1].trim(),
      commentText: match[2].trim(),
    };
  }
  return null;
};

const formatAssessorDisplay = (assessorType: string) => {
  // Display only the company/agency information, no personal names
  return `(${assessorType})`;
};

export default function CommentsSentimentsPanel({
  focusType,
  focusValue,
  wave,
  height,
}: CommentsSentimentsPanelProps) {
  const [comments, setComments] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [copySuccess, setCopySuccess] = useState(false);
  const theme = useTheme();

  useEffect(() => {
    const fetchComments = async () => {
      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams({
          focusType,
          focusValue,
        });

        // Add wave filter if provided and not focusing on wave itself
        if (wave && focusType !== 'wave') {
          params.append('wave', wave);
        }

        const response = await fetch(
          `/api/responses/comments?${params.toString()}`
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Comments API error:', {
            status: response.status,
            statusText: response.statusText,
            url: `/api/responses/comments?${params.toString()}`,
            errorText,
          });
          throw new Error(
            `Failed to fetch comments (${response.status}): ${errorText}`
          );
        }

        const data = await response.json();
        setComments(data.comments || []);
      } catch (err) {
        console.error('Error fetching comments:', err);
        setError('Failed to load comments');
        setComments([
          `Unable to load comments for ${focusValue}. Please try again.`,
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchComments();
  }, [focusType, focusValue, wave]);

  const getAssessorColor = (assessorType: string) => {
    // Match the colors from DashboardBarChart legend
    if (assessorType.includes('Anheuser Busch')) {
      return theme.palette.primary.main; // Agency → AB
    }
    return theme.palette.secondary.main; // AB → Agency
  };

  const renderComment = (comment: string, idx: number) => {
    const parsed = parseComment(comment);

    if (!parsed) {
      // Fallback for comments that don't match expected format
      return (
        <Typography key={idx} variant="body2" color="text.secondary">
          {comment}
        </Typography>
      );
    }

    const assessorColor = getAssessorColor(parsed.assessorType);

    return (
      <Box key={idx} sx={{ mb: 1 }}>
        <Typography
          component="span"
          variant="body2"
          sx={{
            fontStyle: 'italic',
            color: assessorColor,
            // fontWeight: 500,
            fontSize: '0.75rem',
            borderLeft: `2px solid ${assessorColor}`,
            pl: 1,
            // textDecoration: 'underline',
            // textDecorationColor: assessorColor,
            // textUnderlineOffset: '2px',
            // textDecorationThickness: '2px',
          }}
        >
          {formatAssessorDisplay(parsed.assessorType)}
        </Typography>
        <Typography
          component="span"
          variant="body2"
          color="text.secondary"
          sx={{ ml: 1 }}
        >
          {parsed.commentText}
        </Typography>
      </Box>
    );
  };

  const handleCopyToClipboard = async () => {
    try {
      // Format comments for clipboard
      const formattedComments = comments
        .filter((comment) => !comment.includes('Unable to load comments'))
        .join('\n\n');

      const clipboardText = `${focusValue} Comments\n${'='.repeat(
        focusValue.length + 9
      )}\n\n${formattedComments}`;

      await navigator.clipboard.writeText(clipboardText);
      setCopySuccess(true);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const handleCloseSnackbar = () => {
    setCopySuccess(false);
  };

  return (
    <DashboardPanel
      sx={{
        // p: 2,
        // bgcolor: '#e3f2fd',
        height: height || 'auto',
        flexShrink: 1,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 1,
        }}
      >
        <PanelHeader>{focusValue} Comments</PanelHeader>
        <Tooltip title="Copy all comments to clipboard">
          <IconButton
            onClick={handleCopyToClipboard}
            size="small"
            disabled={
              loading ||
              comments.length === 0 ||
              comments[0]?.includes('Unable to load comments')
            }
            sx={{
              color: theme.palette.text.secondary,
              '&:hover': {
                color: theme.palette.primary.main,
              },
            }}
          >
            <ContentCopyIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      {loading ? (
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <CircularProgress size={24} />
        </Box>
      ) : (
        <Box
          sx={{
            flex: 1,
            overflow: 'auto',
            display: 'flex',
            flexDirection: 'column',
            gap: 1,
          }}
        >
          {error && (
            <Typography variant="body2" color="error" sx={{ mb: 1 }}>
              {error}
            </Typography>
          )}
          {comments.map((comment: string, idx: number) =>
            renderComment(comment, idx)
          )}
        </Box>
      )}

      <Snackbar
        open={copySuccess}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity="success"
          sx={{ width: '100%' }}
        >
          Comments copied to clipboard!
        </Alert>
      </Snackbar>
    </DashboardPanel>
  );
}
