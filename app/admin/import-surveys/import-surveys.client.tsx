'use client';

import { useState, useRef, ChangeEvent } from 'react';
import {
  Box,
  Typography,
  Button,
  TextField,
  Input,
  Alert,
} from '@mui/material';
import { IWaveInput } from '../../../lib/models/wave';
import { ISurveyImport } from '../../../lib/models/survey';
import { addWave } from '../../../lib/api/waves';
import { parse, ParseResult } from 'papaparse';
import { expectedSurveyHeaders } from '../../_constants/expected-seed-headers';
import CircularProgress from '@mui/material/CircularProgress';
import DashboardPanel from '../dashboard/DashboardPanel';
import { useTheme } from '@mui/material/styles';

interface ImportSurveysClientProps {
  pageTitle?: string;
}

const ImportSurveysClient = ({
  pageTitle = 'Import Surveys',
}: ImportSurveysClientProps) => {
  const theme = useTheme();
  const [name, setName] = useState<string>(
    `${new Date().getFullYear()}_Seed_Data`
  );
  const [importFilename, setImportFilename] = useState<string>('');
  const [surveyImports, setSurveyImports] = useState<ISurveyImport[]>([]);
  const [importError, setImportError] = useState<string>('');
  const [isCreating, setIsCreating] = useState<boolean>(false);
  const [createWaveError, setCreateWaveError] = useState<string>('');
  const [successMessage, setSuccessMessage] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const nameOnChange = (event: ChangeEvent<HTMLInputElement>) => {
    setName(event.target.value);
  };

  const clearSelectedFileOnClick = () => {
    setImportFilename('');
    setSurveyImports([]);
    setImportError('');
    setSuccessMessage('');
  };

  const createWaveOnClick = async () => {
    setCreateWaveError('');
    setSuccessMessage('');
    setIsCreating(true);

    try {
      const newWave: IWaveInput = { name, status: 'seed' };
      const result = await addWave(newWave, surveyImports);

      if (result.success) {
        setSuccessMessage(
          `Seed data "${name}" created successfully with ${surveyImports.length} surveys!`
        );
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'An unknown error occurred';
      setCreateWaveError(errorMessage);
    }

    setIsCreating(false);
  };

  const processFile = (file: File): void => {
    setImportError('');
    setCreateWaveError('');

    const complete = async (results: ParseResult<string[]>): Promise<void> => {
      const headerLocations = expectedSurveyHeaders.map((header: string) =>
        results.data[0].indexOf(header)
      );

      if (headerLocations.includes(-1)) {
        setImportError(
          `CSV must contain headers: ${expectedSurveyHeaders.join(', ')}`
        );
        return;
      }

      const newSurveyImports: ISurveyImport[] = results.data
        .slice(1) // skip header row
        .map((row: string[]) => ({
          accountName: row[headerLocations[0]],
          agencyName: row[headerLocations[1]],
          agencyType: row[headerLocations[2]],
          brand: row[headerLocations[3]],
          country: row[headerLocations[4]],
          region: row[headerLocations[5]],
          assessmentType: row[headerLocations[6]],
          userName: row[headerLocations[7]],
          userEmail: row[headerLocations[8]],
          userStatus: row[headerLocations[9]],
          inScope: row[headerLocations[10]],
          notes: row[headerLocations[11]] || '',
        }));

      const hasEmptyField: boolean = newSurveyImports.some((obj) =>
        Object.entries(obj).some(
          ([key, value]) => key !== 'notes' && value === '' // notes can be empty
        )
      );

      if (hasEmptyField) {
        setImportError('CSV contains one or more empty required fields.');
        return;
      }

      setImportFilename(file.name);
      setSurveyImports(newSurveyImports);
    };

    const error = (error: Error): void => {
      setImportError(`Parsing error: ${error.message}`);
    };

    parse<string[]>(file, { complete, error });
  };

  // Handle file input change
  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      processFile(e.target.files[0]);
    }
  };

  // Handle drag and drop
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      processFile(e.dataTransfer.files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  return (
    <Box>
      <Typography
        variant="h4"
        fontWeight={700}
        color="text.primary"
        gutterBottom
      >
        {pageTitle}
      </Typography>
      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
        Create seed data template for launching new waves. This data will be
        used as a template when creating actual survey waves.
      </Typography>
      <DashboardPanel sx={{ maxWidth: 400, mt: 2, mb: 4 }}>
        <Typography variant="subtitle1" fontWeight={600} gutterBottom>
          Seed Data Name
        </Typography>
        <TextField
          fullWidth
          size="small"
          value={name}
          onChange={nameOnChange}
          sx={{ mb: 3 }}
        />
        <Typography variant="subtitle1" fontWeight={600} gutterBottom>
          Select Surveys (CSV)
        </Typography>
        {importFilename ? (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              Survey file selected.
            </Typography>
            <Typography variant="body2" sx={{ mb: 1 }}>
              {`${surveyImports.length} total surveys from ${importFilename}`}
            </Typography>
            <Button
              variant="outlined"
              color="secondary"
              onClick={clearSelectedFileOnClick}
            >
              Choose different file
            </Button>
          </Box>
        ) : (
          <Box
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            sx={{
              border: `2px dashed ${
                theme.palette.mode === 'dark' ? '#06406F' : '#90caf9'
              }`,
              borderRadius: 2,
              p: 3,
              textAlign: 'center',
              mb: 2,
              bgcolor: theme.palette.mode === 'dark' ? '#020609' : '#f5fafd',
              cursor: 'pointer',
            }}
            onClick={() => fileInputRef.current?.click()}
          >
            <Input
              inputRef={fileInputRef}
              type="file"
              inputProps={{ accept: '.csv' }}
              onChange={handleFileChange}
              sx={{ display: 'none' }}
            />
            <Typography variant="body2" color="text.secondary">
              Drag and drop a CSV file here, or click to browse
            </Typography>
          </Box>
        )}
        {importError && <Alert severity="error">{importError}</Alert>}
      </DashboardPanel>
      {createWaveError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {createWaveError}
        </Alert>
      )}
      {successMessage && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {successMessage}
        </Alert>
      )}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <Button
          variant="contained"
          color="primary"
          onClick={createWaveOnClick}
          disabled={!name || !surveyImports.length || isCreating}
        >
          Create Seed Data
        </Button>
        {isCreating && <CircularProgress size={24} />}
      </Box>
    </Box>
  );
};

export default ImportSurveysClient;
