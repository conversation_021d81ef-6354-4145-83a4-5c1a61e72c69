'use client';

import { useState, useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';
import { useRouter } from 'next/navigation';
import {
  Box,
  Typography,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Menu,
  MenuItem,
  Avatar,
  Chip,
} from '@mui/material';
import {
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
  Dashboard as DashboardIcon,
  Compare as CompareIcon,
  TableChart as TableChartIcon,
  Waves as WavesIcon,
  RocketLaunch as RocketLaunchIcon,
  FileUpload as FileUploadIcon,
  CloudUpload as CloudUploadIcon,
  Person as PersonIcon,
  Logout as LogoutIcon,
  DarkMode as DarkModeIcon,
  LightMode as LightModeIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import Link from 'next/link';
import { useTheme } from '../../_components/theme-provider';

const drawerWidth = 240;
const miniDrawerWidth = 64;

const navigationItems = [
  { text: 'Dashboard', href: '/admin/dashboard', icon: <DashboardIcon /> },
  { text: 'Comparison', href: '/admin/comparison', icon: <CompareIcon /> },
  { text: 'Data Table', href: '/admin/data-table', icon: <TableChartIcon /> },
  { text: 'Waves', href: '/admin/waves', icon: <WavesIcon /> },
  {
    text: 'Launch Wave',
    href: '/admin/launch-wave',
    icon: <RocketLaunchIcon />,
  },
];

const superadminItems = [
  {
    text: 'Import Seed Data',
    href: '/admin/import-surveys',
    icon: <FileUploadIcon />,
  },
  {
    text: 'Import Historic Responses',
    href: '/admin/import-responses',
    icon: <CloudUploadIcon />,
  },
  {
    text: 'Admin Accounts',
    href: '/admin/settings',
    icon: <SettingsIcon />,
  },
];

interface AdminInfo {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'superadmin';
}

export default function AdminSidebar() {
  const pathname = usePathname();
  const router = useRouter();
  const [open, setOpen] = useState(true);
  const [showText, setShowText] = useState(true); // controls text rendering
  const drawerRef = useRef<HTMLDivElement>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [adminInfo, setAdminInfo] = useState<AdminInfo | null>(null);
  const { darkMode, toggleDarkMode } = useTheme();
  const menuOpen = Boolean(anchorEl);

  // Fetch admin session info
  useEffect(() => {
    const fetchAdminInfo = async () => {
      try {
        const response = await fetch('/api/admin/auth/session');
        const data = await response.json();

        if (data.success && data.admin) {
          setAdminInfo(data.admin);
        }
      } catch (error) {
        console.error('Failed to fetch admin session:', error);
      }
    };

    fetchAdminInfo();
  }, []);

  const handleDrawerToggle = () => {
    if (open) setShowText(false); // hide text immediately when collapsing
    setOpen((prev) => !prev);
  };

  const handleTransitionEnd = () => {
    if (open) setShowText(true); // show text after expanding
  };

  const handleUserMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/admin/auth/logout', { method: 'POST' });
      router.push('/admin/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
    handleUserMenuClose();
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((word) => word[0])
      .join('')
      .toUpperCase();
  };

  return (
    <Drawer
      variant="permanent"
      ref={drawerRef}
      sx={{
        width: open ? drawerWidth : miniDrawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: open ? drawerWidth : miniDrawerWidth,
          boxSizing: 'border-box',
          transition: 'width 0.3s',
          overflowX: 'hidden',
          display: 'flex',
          flexDirection: 'column',
        },
      }}
      PaperProps={{
        onTransitionEnd: handleTransitionEnd,
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: open ? 'space-between' : 'center',
          p: 2,
          minHeight: 64,
        }}
      >
        {open && (
          <Typography variant="h6" noWrap>
            ECHO360
          </Typography>
        )}
        <IconButton onClick={handleDrawerToggle}>
          {open ? <ChevronLeftIcon /> : <MenuIcon />}
        </IconButton>
      </Box>

      <Divider />

      {/* Navigation */}
      <List sx={{ flexGrow: 1, p: 1 }}>
        {navigationItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              component={Link}
              href={item.href}
              selected={pathname === item.href}
              sx={{
                minHeight: 48,
                justifyContent: open ? 'initial' : 'center',
                px: 2.5,
              }}
            >
              <ListItemIcon
                sx={{
                  minWidth: 0,
                  mr: open ? 3 : 'auto',
                  justifyContent: 'center',
                }}
              >
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={item.text}
                sx={{
                  opacity: open && showText ? 1 : 0,
                  transition: 'opacity 0.3s',
                  pointerEvents: open && showText ? 'auto' : 'none',
                  whiteSpace: 'nowrap',
                  width: open ? 'auto' : 0,
                  display: open || showText ? 'block' : 'none', // Ensures it's hidden after collapse
                }}
              />
            </ListItemButton>
          </ListItem>
        ))}

        {/* Superadmin-only items */}
        {adminInfo?.role === 'superadmin' && (
          <>
            <Divider sx={{ my: 1 }} />
            {superadminItems.map((item) => (
              <ListItem key={item.text} disablePadding>
                <ListItemButton
                  component={Link}
                  href={item.href}
                  sx={{
                    minHeight: 48,
                    justifyContent: open ? 'initial' : 'center',
                    px: 2.5,
                  }}
                >
                  <ListItemIcon
                    sx={{
                      minWidth: 0,
                      mr: open ? 3 : 'auto',
                      justifyContent: 'center',
                    }}
                  >
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText
                    primary={item.text}
                    sx={{
                      opacity: open && showText ? 1 : 0,
                      transition: 'opacity 0.3s',
                      pointerEvents: open && showText ? 'auto' : 'none',
                      whiteSpace: 'nowrap',
                      width: open ? 'auto' : 0,
                      display: open || showText ? 'block' : 'none', // Ensures it's hidden after collapse
                    }}
                  />
                </ListItemButton>
              </ListItem>
            ))}
          </>
        )}
      </List>

      <Divider />

      {/* Dark Mode Toggle */}
      <Box sx={{ p: 1 }}>
        <ListItemButton
          onClick={toggleDarkMode}
          sx={{
            minHeight: 48,
            justifyContent: open ? 'initial' : 'center',
            px: 2.5,
            borderRadius: 1,
          }}
        >
          <ListItemIcon
            sx={{
              minWidth: 0,
              mr: open ? 3 : 'auto',
              justifyContent: 'center',
            }}
          >
            {darkMode ? <LightModeIcon /> : <DarkModeIcon />}
          </ListItemIcon>
          {open && (
            <ListItemText
              primary="Dark Mode"
              sx={{
                opacity: open && showText ? 1 : 0,
                transition: 'opacity 0.3s',
                pointerEvents: open && showText ? 'auto' : 'none',
                whiteSpace: 'nowrap',
                width: open ? 'auto' : 0,
                display: open || showText ? 'block' : 'none', // Ensures it's hidden after collapse
              }}
            />
          )}
        </ListItemButton>
      </Box>

      {/* User Menu */}
      <Box sx={{ p: 1 }}>
        <ListItemButton
          onClick={handleUserMenuClick}
          sx={{
            minHeight: 48,
            justifyContent: open ? 'initial' : 'center',
            px: 2.5,
            borderRadius: 1,
          }}
        >
          <ListItemIcon
            sx={{
              minWidth: 0,
              mr: open ? 3 : 'auto',
              justifyContent: 'center',
            }}
          >
            <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
              {adminInfo ? getInitials(adminInfo.name) : <PersonIcon />}
            </Avatar>
          </ListItemIcon>
          {open && (
            <Box sx={{ opacity: 1, flex: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                {adminInfo?.name || 'Admin'}
              </Typography>
              {adminInfo && (
                <Chip
                  label={
                    adminInfo.role === 'superadmin' ? 'Super Admin' : 'Admin'
                  }
                  size="small"
                  color={
                    adminInfo.role === 'superadmin' ? 'primary' : 'default'
                  }
                  sx={{ mt: 0.5 }}
                />
              )}
            </Box>
          )}
        </ListItemButton>
      </Box>

      {/* User Dropdown Menu */}
      <Menu
        anchorEl={anchorEl}
        open={menuOpen}
        onClose={handleUserMenuClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <MenuItem onClick={handleLogout}>
          <ListItemIcon>
            <LogoutIcon />
          </ListItemIcon>
          <Typography>Logout</Typography>
        </MenuItem>
      </Menu>
    </Drawer>
  );
}
