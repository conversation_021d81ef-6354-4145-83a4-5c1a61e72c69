'use client';

import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  Typography,
  useTheme,
} from '@mui/material';
import DashboardPanel from '../dashboard/DashboardPanel';

export interface DataTableColumn {
  id: string;
  label: string;
}

export type Order = 'asc' | 'desc';

export interface DataTableRow {
  [key: string]: string | number;
}

export interface DataTableTableProps {
  rows: DataTableRow[];
  columns: DataTableColumn[];
  order: Order;
  orderBy: string;
  handleSort: (property: string) => void;
}

export default function DataTableTable({
  rows,
  columns,
  order,
  orderBy,
  handleSort,
}: DataTableTableProps) {
  const theme = useTheme();

  const formatValue = (value: string | number) => {
    if (typeof value === 'number') {
      return value.toFixed(1);
    }
    return value;
  };

  return (
    <DashboardPanel>
      <Typography variant="subtitle1" fontWeight={600} gutterBottom>
        Assessment Breakdown by Category
      </Typography>
      <TableContainer sx={{ width: '100%', overflowX: 'hidden' }}>
        <Table
          size="small"
          stickyHeader
          sx={{
            borderTop: `1px solid ${theme.palette.divider}`,
            width: '100%',
            tableLayout: 'fixed',
            '& .MuiTableCell-root': {
              wordWrap: 'break-word',
              whiteSpace: 'normal',
              overflow: 'hidden',
            },
          }}
        >
          <TableHead>
            <TableRow
              sx={{
                backgroundColor: theme.palette.action.hover,
                '& .MuiTableCell-head': {
                  fontWeight: 600,
                  backgroundColor: theme.palette.action.hover,
                },
              }}
            >
              {columns.map((col) => (
                <TableCell
                  key={col.id}
                  sx={{ fontWeight: 700 }}
                  sortDirection={orderBy === col.id ? order : false}
                >
                  <TableSortLabel
                    active={orderBy === col.id}
                    direction={orderBy === col.id ? order : 'desc'}
                    onClick={() => handleSort(col.id)}
                  >
                    {col.label}
                  </TableSortLabel>
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {rows.map((row, idx) => (
              <TableRow
                key={`${row.agencyName || row.agency}-${idx}`}
                sx={{
                  backgroundColor:
                    idx % 2 === 0
                      ? theme.palette.background.paper
                      : theme.palette.action.hover,
                  '&:hover': {
                    backgroundColor: theme.palette.action.selected,
                  },
                }}
              >
                {columns.map((col) => (
                  <TableCell
                    key={col.id}
                    align={typeof row[col.id] === 'number' ? 'right' : 'left'}
                  >
                    {formatValue(row[col.id])}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </DashboardPanel>
  );
}
