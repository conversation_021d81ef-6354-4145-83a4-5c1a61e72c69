'use client';

import {
  Box,
  Typography,
  ToggleButtonGroup,
  ToggleButton,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  Stack,
  CircularProgress,
  Divider,
  OutlinedInput,
  ListItemText,
  Checkbox,
  Autocomplete,
  TextField,
} from '@mui/material';
import { useState, useEffect, useMemo } from 'react';
import ComparisonBarChart from './ComparisonBarChart';
import ComparisonTable from './ComparisonTable';
import DashboardPanel from '../dashboard/DashboardPanel';
import PanelHeader from '../dashboard/PanelHeader';
import PanelSubheader from '../dashboard/PanelSubheader';
import ElevatedFormControl from '../../_components/ElevatedFormControl';
import { SelectChangeEvent } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { RestartAlt as ResetIcon } from '@mui/icons-material';
interface FilterOptions {
  agencies: string[];
  brands: string[];
  regions: string[];
  waves: string[];
}

export default function ComparisonClient() {
  const [focus, setFocus] = useState<'brand' | 'agency'>('agency');
  const [compareBrand, setCompareBrand] = useState('');
  const [brandsToCompare, setBrandsToCompare] = useState<string[]>([]);
  const [compareAgency, setCompareAgency] = useState('');
  const [agenciesToCompare, setAgenciesToCompare] = useState<string[]>([]);
  const [selectedWaves, setSelectedWaves] = useState<string[]>([]);
  const [tempSelectedWaves, setTempSelectedWaves] = useState<string[]>([]);
  const [selectedAgency, setSelectedAgency] = useState('All Agencies');
  const [selectedRegion, setSelectedRegion] = useState('All Regions');
  const [selectedBrand, setSelectedBrand] = useState('All Brands');
  const [respondent, setRespondent] = useState('All');

  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    agencies: ['All Agencies'],
    brands: ['All Brands'],
    regions: ['All Regions'],
    waves: [],
  });
  const [availableFilterOptions, setAvailableFilterOptions] =
    useState<FilterOptions>({
      agencies: ['All Agencies'],
      brands: ['All Brands'],
      regions: ['All Regions'],
      waves: [],
    });
  const [loading, setLoading] = useState(true);

  const theme = useTheme();

  // Fetch initial filter options
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setLoading(true);

        // Fetch initial filter options
        const [
          brandsResponse,
          regionsResponse,
          agenciesResponse,
          wavesResponse,
        ] = await Promise.all([
          fetch('/api/surveys/unique-brands'),
          fetch('/api/surveys/unique-regions'),
          fetch('/api/surveys/unique-agencies'),
          fetch('/api/waves?excludeSeed=true'),
        ]);

        const [agencies, brands, regions, waves] = await Promise.all([
          agenciesResponse.json(),
          brandsResponse.json(),
          regionsResponse.json(),
          wavesResponse.json(),
        ]);

        const waveNames = waves.map((w: { name: string }) => w.name);
        const initialFilterOptions = {
          agencies: ['All Agencies', ...agencies],
          brands: ['All Brands', ...brands],
          regions: ['All Regions', ...regions],
          waves: waveNames,
        };

        setFilterOptions(initialFilterOptions);
        setAvailableFilterOptions(initialFilterOptions);

        // Set defaults
        if (waveNames.length > 0) {
          const latestWave = waveNames[0]; // First item is newest since API sorts by createdAt: -1
          setSelectedWaves([latestWave]);
          setTempSelectedWaves([latestWave]);
        }

        if (brands.length > 0) {
          setCompareBrand(brands[0]); // First brand instead of second
          // Don't auto-select items for comparison - let user choose
        }

        if (agencies.length > 0) {
          setCompareAgency(agencies[0]); // First agency instead of second
          // Don't auto-select items for comparison - let user choose
        }
      } catch (error) {
        console.error('Error fetching comparison data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchInitialData();
  }, []);

  // Update available filter options when selections change
  useEffect(() => {
    const updateAvailableOptions = async () => {
      try {
        const params = new URLSearchParams();

        // Always include selected waves as the primary filter
        if (selectedWaves.length > 0) {
          params.append('wave', selectedWaves.join(','));
        }

        if (selectedAgency !== 'All Agencies') {
          params.append('agency', selectedAgency);
        }
        if (selectedBrand !== 'All Brands') {
          params.append('brand', selectedBrand);
        }
        if (selectedRegion !== 'All Regions') {
          params.append('region', selectedRegion);
        }

        const response = await fetch(
          `/api/surveys/filtered-options?${params.toString()}`
        );
        const data = await response.json();

        setAvailableFilterOptions({
          agencies: data.agencies || filterOptions.agencies,
          brands: data.brands || filterOptions.brands,
          regions: data.regions || filterOptions.regions,
          waves: filterOptions.waves, // Waves don't filter based on other selections
        });
      } catch (error) {
        console.error('Error fetching filtered options:', error);
      }
    };

    // Only update if we have initial data and waves are selected
    if (filterOptions.agencies.length > 1 && selectedWaves.length > 0) {
      updateAvailableOptions();
    }
  }, [
    selectedWaves,
    selectedAgency,
    selectedBrand,
    selectedRegion,
    filterOptions,
  ]);

  // Memoize chart and table props to prevent unnecessary re-renders
  const chartProps = useMemo(
    () => ({
      focus,
      itemsToCompare: focus === 'brand' ? brandsToCompare : agenciesToCompare,
      agency: selectedAgency,
      brand: selectedBrand,
      region: selectedRegion,
      wave: selectedWaves.join(','),
      respondentType: respondent,
      height: 320,
    }),
    [
      focus,
      brandsToCompare,
      agenciesToCompare,
      selectedAgency,
      selectedBrand,
      selectedRegion,
      selectedWaves,
      respondent,
    ]
  );

  const tableProps = useMemo(
    () => ({
      focus,
      itemsToCompare: focus === 'brand' ? brandsToCompare : agenciesToCompare,
      agency: selectedAgency,
      brand: selectedBrand,
      region: selectedRegion,
      wave: selectedWaves.join(','),
      respondentType: respondent,
    }),
    [
      focus,
      brandsToCompare,
      agenciesToCompare,
      selectedAgency,
      selectedBrand,
      selectedRegion,
      selectedWaves,
      respondent,
    ]
  );

  const handleAddBrand = () => {
    if (
      compareBrand &&
      !brandsToCompare.includes(compareBrand) &&
      brandsToCompare.length < 5
    ) {
      setBrandsToCompare([...brandsToCompare, compareBrand]);
    }
  };

  const handleRemoveBrand = (brand: string) => {
    setBrandsToCompare(brandsToCompare.filter((b) => b !== brand));
  };

  const handleAddAgency = () => {
    if (
      compareAgency &&
      !agenciesToCompare.includes(compareAgency) &&
      agenciesToCompare.length < 5
    ) {
      setAgenciesToCompare([...agenciesToCompare, compareAgency]);
    }
  };

  const handleRemoveAgency = (agency: string) => {
    setAgenciesToCompare(agenciesToCompare.filter((a) => a !== agency));
  };

  const handleAgencyChange = (
    event: React.SyntheticEvent,
    value: string | null
  ) => {
    setSelectedAgency(value || 'All Agencies');
  };

  const handleRegionChange = (
    event: React.SyntheticEvent,
    value: string | null
  ) => {
    setSelectedRegion(value || 'All Regions');
  };

  const handleWaveChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setTempSelectedWaves(typeof value === 'string' ? value.split(',') : value);
  };

  const handleWaveClose = () => {
    setSelectedWaves(tempSelectedWaves);
  };

  const handleCompareBrandChange = (event: SelectChangeEvent) => {
    setCompareBrand(event.target.value);
  };

  const handleBrandChange = (
    event: React.SyntheticEvent,
    value: string | null
  ) => {
    setSelectedBrand(value || 'All Brands');
  };

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: 400,
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
      <DashboardPanel
        sx={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'start',
          gap: 0,
          p: 0,
        }}
      >
        {/* Focus Toggle */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 1,
            p: 2,
          }}
        >
          <PanelSubheader>Comparison Focus</PanelSubheader>
          <ElevatedFormControl>
            <ToggleButtonGroup
              color="primary"
              orientation="vertical"
              size="small"
              value={focus}
              exclusive
              onChange={(_, v) => v && setFocus(v)}
            >
              <ToggleButton value="agency">Agency</ToggleButton>
              <ToggleButton value="brand">Brand</ToggleButton>
            </ToggleButtonGroup>
          </ElevatedFormControl>
        </Box>

        <Divider orientation="vertical" flexItem />

        {/* Compare Brand/Agency Selector */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
            px: 4,
            py: 2,
          }}
        >
          <PanelSubheader>
            {focus === 'brand'
              ? 'Add Brands to Compare'
              : 'Add Agencies to Compare'}
          </PanelSubheader>
          <Box
            sx={{
              display: 'flex',
              gap: 2,
              mb: 2,
              alignItems: 'center',
              flexWrap: 'wrap',
            }}
          >
            <ElevatedFormControl size="small" sx={{ minWidth: 180 }}>
              <Select
                value={focus === 'brand' ? compareBrand : compareAgency}
                onChange={
                  focus === 'brand'
                    ? handleCompareBrandChange
                    : (e) => setCompareAgency(e.target.value)
                }
                sx={{
                  maxWidth: 250,
                }}
              >
                {(focus === 'brand'
                  ? availableFilterOptions.brands.filter(
                      (b) => b !== 'All Brands'
                    )
                  : availableFilterOptions.agencies.filter(
                      (a) => a !== 'All Agencies'
                    )
                ).map((item) => (
                  <MenuItem
                    key={item}
                    value={item}
                    disabled={(focus === 'brand'
                      ? brandsToCompare
                      : agenciesToCompare
                    ).includes(item)}
                  >
                    {item}
                  </MenuItem>
                ))}
              </Select>
            </ElevatedFormControl>
            <ElevatedFormControl>
              <Button
                size="small"
                variant="contained"
                onClick={focus === 'brand' ? handleAddBrand : handleAddAgency}
                disabled={
                  focus === 'brand'
                    ? !compareBrand ||
                      brandsToCompare.includes(compareBrand) ||
                      brandsToCompare.length >= 5
                    : !compareAgency ||
                      agenciesToCompare.includes(compareAgency) ||
                      agenciesToCompare.length >= 5
                }
              >
                Add {focus === 'brand' ? 'Brand' : 'Agency'}
              </Button>
            </ElevatedFormControl>

            {/* Reset button - only show when there are items to reset */}
            {(focus === 'brand'
              ? brandsToCompare.length > 0
              : agenciesToCompare.length > 0) && (
              <Button
                size="small"
                variant="text"
                startIcon={<ResetIcon />}
                onClick={() => {
                  if (focus === 'brand') {
                    setBrandsToCompare([]);
                  } else {
                    setAgenciesToCompare([]);
                  }
                }}
              >
                Reset
              </Button>
            )}
          </Box>

          {/* Show max items message */}
          {(focus === 'brand'
            ? brandsToCompare.length
            : agenciesToCompare.length) >= 5 && (
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ px: 4, pb: 1 }}
            >
              Maximum of 5 items can be compared
            </Typography>
          )}
        </Box>
        <Box sx={{ px: 4, py: 1 }}>
          <Stack direction="column" spacing={1} sx={{ alignItems: 'end' }}>
            {(focus === 'brand' ? brandsToCompare : agenciesToCompare).map(
              (item: string, idx: number) => {
                // Define colors that match the chart
                const colors = [
                  '#1976d2', // Primary blue
                  '#d32f2f', // Red/orange
                  '#2e7d32', // Green
                  '#ed6c02', // Orange
                  '#9c27b0', // Purple
                ];

                return (
                  <Chip
                    key={item}
                    label={item}
                    size="small"
                    onDelete={() =>
                      focus === 'brand'
                        ? handleRemoveBrand(item)
                        : handleRemoveAgency(item)
                    }
                    variant="outlined"
                    sx={{
                      borderColor: colors[idx % colors.length],
                      color: colors[idx % colors.length],
                      '& .MuiChip-deleteIcon': {
                        color: colors[idx % colors.length],
                      },
                    }}
                  />
                );
              }
            )}
          </Stack>
        </Box>
      </DashboardPanel>

      <DashboardPanel>
        {/* Filters */}
        <Box>
          <Box
            sx={{
              display: 'flex',
              gap: 2,
              alignItems: 'center',
              flexWrap: 'wrap',
            }}
          >
            <PanelSubheader>Filters:</PanelSubheader>
            <ElevatedFormControl
              size="small"
              sx={{ width: '100%', maxWidth: 300 }}
            >
              <InputLabel>Wave</InputLabel>
              <Select
                multiple
                value={tempSelectedWaves}
                onChange={handleWaveChange}
                onClose={handleWaveClose}
                input={<OutlinedInput label="Wave" />}
                renderValue={(selected) => {
                  if (selected.length === 0) return '';
                  if (selected.length === 1) return selected[0];
                  if (selected.length <= 3) return selected.join(', ');
                  return `${selected.slice(0, 2).join(', ')} +${selected.length - 2} more`;
                }}
              >
                {filterOptions.waves.map((wave) => (
                  <MenuItem key={wave} value={wave}>
                    <Checkbox checked={tempSelectedWaves.indexOf(wave) > -1} />
                    <ListItemText primary={wave} />
                  </MenuItem>
                ))}
              </Select>
            </ElevatedFormControl>
            {focus !== 'agency' && (
              <ElevatedFormControl size="small">
                <Autocomplete
                  options={availableFilterOptions.agencies}
                  value={selectedAgency}
                  onChange={handleAgencyChange}
                  renderInput={(params) => (
                    <TextField {...params} label="Agency" size="small" />
                  )}
                  size="small"
                  sx={{ minWidth: 140 }}
                  disableClearable
                />
              </ElevatedFormControl>
            )}
            {focus !== 'brand' && (
              <ElevatedFormControl size="small">
                <Autocomplete
                  options={availableFilterOptions.brands}
                  value={selectedBrand}
                  onChange={handleBrandChange}
                  renderInput={(params) => (
                    <TextField {...params} label="Brand" size="small" />
                  )}
                  size="small"
                  sx={{ minWidth: 140 }}
                  disableClearable
                />
              </ElevatedFormControl>
            )}
            <ElevatedFormControl size="small">
              <Autocomplete
                options={availableFilterOptions.regions}
                value={selectedRegion}
                onChange={handleRegionChange}
                renderInput={(params) => (
                  <TextField {...params} label="Region" size="small" />
                )}
                size="small"
                sx={{ minWidth: 140 }}
                disableClearable
              />
            </ElevatedFormControl>
            <ElevatedFormControl size="small" sx={{ minWidth: 180 }}>
              <InputLabel>Respondent Type</InputLabel>
              <Select
                label="Respondent Type"
                value={respondent}
                onChange={(e) => setRespondent(e.target.value)}
              >
                <MenuItem value="All">All</MenuItem>
                <MenuItem value="ABI on Agency">ABI on Agency</MenuItem>
                <MenuItem value="Agency on ABI">Agency on ABI</MenuItem>
              </Select>
            </ElevatedFormControl>
          </Box>
        </Box>
      </DashboardPanel>

      {/* Show message if no items selected or no waves selected */}
      {((focus === 'brand'
        ? brandsToCompare.length === 0
        : agenciesToCompare.length === 0) ||
        selectedWaves.length === 0) && (
        <Box
          sx={{
            mt: 4,
            p: 3,
            textAlign: 'center',
            bgcolor: theme.palette.mode === 'dark' ? 'grey.900' : 'grey.100',
            borderRadius: 1,
          }}
        >
          <Typography variant="h6" color="text.secondary">
            {selectedWaves.length === 0
              ? 'No Waves Selected'
              : focus === 'brand'
              ? 'No Brands Selected'
              : 'No Agencies Selected'}
          </Typography>
          <Typography color="text.secondary">
            {selectedWaves.length === 0
              ? 'Please select at least one wave to view comparison data.'
              : `Please select at least one ${focus} to compare.`}
          </Typography>
        </Box>
      )}

      {/* Main content - only show if waves and items are selected */}
      {selectedWaves.length > 0 &&
        (focus === 'brand'
          ? brandsToCompare.length > 0
          : agenciesToCompare.length > 0) && (
          <>
            {/* Chart Area */}
            <DashboardPanel>
              <PanelHeader sx={{ textAlign: 'center' }}>
                {focus === 'brand' ? 'Brand' : 'Agency'} Comparison
              </PanelHeader>
              <PanelSubheader sx={{ textAlign: 'center' }}>
                {selectedAgency}, {selectedRegion} - {selectedWaves.join(', ')}
              </PanelSubheader>

              <Box sx={{ width: '100%' }}>
                <ComparisonBarChart {...chartProps} />
              </Box>
            </DashboardPanel>

            {/* Table Area */}
            <ComparisonTable {...tableProps} />
          </>
        )}
    </Box>
  );
}
