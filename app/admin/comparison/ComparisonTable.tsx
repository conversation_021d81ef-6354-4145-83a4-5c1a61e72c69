'use client';

import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  CircularProgress,
  Box,
  useTheme,
} from '@mui/material';
import { useState, useEffect } from 'react';
import DashboardPanel from '../dashboard/DashboardPanel';
import PanelHeader from '../dashboard/PanelHeader';

interface ComparisonTableProps {
  focus: 'brand' | 'wave' | 'agency';
  itemsToCompare: string[];
  agency: string;
  brand: string;
  region: string;
  wave: string;
  respondentType: string;
}

interface ComparisonData {
  categories: string[];
  brands: {
    brand: string;
    data: number[];
    responseCount: number;
  }[];
  summary: {
    brand: string;
    q1Avg: number;
    q2Avg: number;
    q3Avg: number;
    q4Avg: number;
    q5Avg: number;
    responseCount: number;
  }[];
}

export default function ComparisonTable({
  focus,
  itemsToCompare,
  agency,
  brand,
  region,
  wave,
  respondentType,
}: ComparisonTableProps) {
  const [data, setData] = useState<ComparisonData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const theme = useTheme();

  // Color scheme to match ComparisonBarChart
  const getItemColor = (index: number) => {
    const colors = ['#1976d2', '#d32f2f', '#2e7d32', '#ed6c02', '#9c27b0'];
    return colors[index] || '#666';
  };

  useEffect(() => {
    const fetchComparisonData = async () => {
      if (itemsToCompare.length === 0 || !wave) {
        setData(null);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams({
          brands: itemsToCompare.join(','),
          focus,
          wave,
          respondentType,
        });

        if (agency !== 'All Agencies') {
          params.append('agencyName', agency);
        }
        if (brand !== 'All Brands') {
          params.append('brand', brand);
        }
        if (region !== 'All Regions') {
          params.append('region', region);
        }

        const response = await fetch(
          `/api/responses/comparison?${params.toString()}`
        );

        if (!response.ok) {
          throw new Error('Failed to fetch comparison data');
        }

        const result = await response.json();
        setData(result);
      } catch (err) {
        console.error('Error fetching comparison data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchComparisonData();
  }, [itemsToCompare, agency, brand, region, wave, respondentType, focus]);

  if (loading) {
    return (
      <DashboardPanel>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            py: 4,
          }}
        >
          <CircularProgress />
        </Box>
      </DashboardPanel>
    );
  }

  if (error) {
    return (
      <DashboardPanel>
        <PanelHeader gutterBottom>
          {focus === 'brand' ? 'Brand' : focus === 'wave' ? 'Wave' : 'Agency'}{' '}
          Comparison Table
        </PanelHeader>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            py: 4,
          }}
        >
          <Typography color="error" variant="body2">
            Error loading table: {error}
          </Typography>
        </Box>
      </DashboardPanel>
    );
  }

  if (!data || data.brands.length === 0) {
    return (
      <DashboardPanel>
        <PanelHeader gutterBottom>
          {focus === 'brand' ? 'Brand' : focus === 'wave' ? 'Wave' : 'Agency'}{' '}
          Comparison Table
        </PanelHeader>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            py: 4,
          }}
        >
          <Typography color="text.secondary" variant="body2">
            No data available for the selected filters
          </Typography>
        </Box>
      </DashboardPanel>
    );
  }

  return (
    <DashboardPanel>
      <PanelHeader gutterBottom>
        {focus === 'brand' ? 'Brand' : focus === 'wave' ? 'Wave' : 'Agency'}{' '}
        Comparison Table
      </PanelHeader>
      <TableContainer sx={{ width: '100%', overflowX: 'hidden' }}>
        <Table
          size="small"
          stickyHeader
          sx={{
            borderTop: `1px solid ${theme.palette.divider}`,
            width: '100%',
            tableLayout: 'fixed',
            '& .MuiTableCell-root': {
              wordWrap: 'break-word',
              whiteSpace: 'normal',
              overflow: 'hidden',
            },
          }}
        >
          <TableHead>
            <TableRow
              sx={{
                backgroundColor: theme.palette.action.hover,
                '& .MuiTableCell-head': {
                  fontWeight: 600,
                  backgroundColor: theme.palette.action.hover,
                },
              }}
            >
              <TableCell>Category</TableCell>
              {itemsToCompare.map((brand, index) => (
                <TableCell
                  key={brand}
                  align="right"
                  sx={{
                    color: getItemColor(index),
                    fontWeight: 600,
                  }}
                >
                  {brand}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {data.categories.map((category, idx) => (
              <TableRow
                key={category}
                sx={{
                  backgroundColor:
                    idx % 2 === 0
                      ? theme.palette.background.paper
                      : theme.palette.action.hover,
                  '&:hover': {
                    backgroundColor: theme.palette.action.selected,
                  },
                }}
              >
                <TableCell sx={{ fontWeight: 600 }}>{category}</TableCell>
                {itemsToCompare.map((brand, brandIndex) => {
                  const brandData = data.brands.find((b) => b.brand === brand);
                  const value = brandData?.data[idx];
                  return (
                    <TableCell
                      key={brand}
                      align="right"
                      sx={{
                        color: getItemColor(brandIndex),
                        fontWeight: 500,
                      }}
                    >
                      {value !== undefined && value !== null
                        ? value.toFixed(2)
                        : '-'}
                    </TableCell>
                  );
                })}
              </TableRow>
            ))}
            {/* Add a separator row */}
            <TableRow>
              <TableCell
                colSpan={itemsToCompare.length + 1}
                sx={{ borderBottom: 'none', py: 1 }}
              />
            </TableRow>
            {/* Add response counts row */}
            <TableRow
              sx={{
                backgroundColor: theme.palette.action.hover,
                '&:hover': {
                  backgroundColor: theme.palette.action.selected,
                },
              }}
            >
              <TableCell sx={{ fontWeight: 700 }}>Response Count</TableCell>
              {itemsToCompare.map((brand, index) => {
                const brandData = data.summary.find((b) => b.brand === brand);
                return (
                  <TableCell
                    key={brand}
                    align="right"
                    sx={{
                      fontWeight: 600,
                      color: getItemColor(index),
                    }}
                  >
                    {brandData?.responseCount || 0}
                  </TableCell>
                );
              })}
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>
    </DashboardPanel>
  );
}
