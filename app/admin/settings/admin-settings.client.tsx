'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Person as PersonIcon,
  Key as KeyIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Add as AddIcon,
} from '@mui/icons-material';
interface AdminInfo {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'superadmin';
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
}

interface CurrentAdminData {
  _id: string;
  email: string;
  name: string;
  role: 'admin' | 'superadmin';
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface AdminSettingsClientProps {
  currentAdmin: CurrentAdminData;
}

export default function AdminSettingsClient({
  currentAdmin,
}: AdminSettingsClientProps) {
  const [admins, setAdmins] = useState<AdminInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [resetDialogOpen, setResetDialogOpen] = useState(false);
  const [selectedAdmin, setSelectedAdmin] = useState<AdminInfo | null>(null);
  const [newPassword, setNewPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [resetLoading, setResetLoading] = useState(false);
  const [resetError, setResetError] = useState('');
  const [resetSuccess, setResetSuccess] = useState('');

  // Create admin dialog state
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);
  const [createError, setCreateError] = useState('');
  const [createSuccess, setCreateSuccess] = useState('');
  const [newAdminData, setNewAdminData] = useState({
    email: '',
    password: '',
    name: '',
    role: 'admin' as 'admin' | 'superadmin',
  });
  const [showCreatePassword, setShowCreatePassword] = useState(false);

  const fetchAdmins = useCallback(async () => {
    // Only fetch admins if user is a superadmin
    if (currentAdmin.role !== 'superadmin') {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/admin/auth/list-admins');
      const data = await response.json();

      if (data.success) {
        setAdmins(data.admins);
      } else {
        setError(data.error || 'Failed to fetch admins');
      }
    } catch (error) {
      console.error('Error fetching admins:', error);
      setError('Failed to fetch admins');
    } finally {
      setLoading(false);
    }
  }, [currentAdmin.role]);

  useEffect(() => {
    console.log('Current admin:', currentAdmin);
    fetchAdmins();
  }, [fetchAdmins, currentAdmin]);

  const handleResetPassword = (admin: AdminInfo) => {
    setSelectedAdmin(admin);
    setNewPassword('');
    setResetError('');
    setResetSuccess('');
    setResetDialogOpen(true);
  };

  const handleResetDialogClose = () => {
    setResetDialogOpen(false);
    setSelectedAdmin(null);
    setNewPassword('');
    setShowPassword(false);
    setResetError('');
    setResetSuccess('');
  };

  const handleSubmitPasswordReset = async () => {
    if (!selectedAdmin || !newPassword) return;

    try {
      setResetLoading(true);
      setResetError('');

      const response = await fetch('/api/admin/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          adminId: selectedAdmin.id,
          newPassword,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setResetSuccess('Password reset successfully');
        setTimeout(() => {
          handleResetDialogClose();
        }, 2000);
      } else {
        setResetError(data.error || 'Password reset failed');
      }
    } catch (error) {
      console.error('Password reset error:', error);
      setResetError('Password reset failed');
    } finally {
      setResetLoading(false);
    }
  };

  const handleCreateAdmin = () => {
    setCreateDialogOpen(true);
    setNewAdminData({
      email: '',
      password: '',
      name: '',
      role: 'admin',
    });
    setCreateError('');
    setCreateSuccess('');
  };

  const handleCreateDialogClose = () => {
    setCreateDialogOpen(false);
    setNewAdminData({
      email: '',
      password: '',
      name: '',
      role: 'admin',
    });
    setShowCreatePassword(false);
    setCreateError('');
    setCreateSuccess('');
  };

  const handleSubmitCreateAdmin = async () => {
    if (!newAdminData.email || !newAdminData.password || !newAdminData.name)
      return;

    try {
      setCreateLoading(true);
      setCreateError('');

      const response = await fetch('/api/admin/auth/create-admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newAdminData),
      });

      const data = await response.json();

      if (data.success) {
        setCreateSuccess('Admin account created successfully');
        // Refresh the admin list
        await fetchAdmins();
        setTimeout(() => {
          handleCreateDialogClose();
        }, 2000);
      } else {
        setCreateError(data.error || 'Failed to create admin account');
      }
    } catch (error) {
      console.error('Create admin error:', error);
      setCreateError('Failed to create admin account');
    } finally {
      setCreateLoading(false);
    }
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {currentAdmin.role !== 'superadmin' && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          You need superadmin privileges to manage admin accounts. Only
          superadmins can view, create, and reset admin passwords.
        </Alert>
      )}

      <Box
        sx={{
          mb: 3,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Typography variant="h5" component="h2">
          Admin Accounts
          <Typography variant="body1" color="text.secondary">
            Manage admin accounts and reset passwords
          </Typography>
        </Typography>
        {currentAdmin.role === 'superadmin' && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateAdmin}
          >
            Create Admin Account
          </Button>
        )}
      </Box>

      {currentAdmin.role === 'superadmin' ? (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Admin</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Last Login</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {admins.map((admin) => (
                  <TableRow key={admin.id}>
                    <TableCell>
                      <Box
                        sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                      >
                        <PersonIcon color="action" />
                        <Typography variant="body2" fontWeight={500}>
                          {admin.name}
                        </Typography>
                        {admin.id === currentAdmin._id && (
                          <Chip label="You" size="small" color="primary" />
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{admin.email}</Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={
                          admin.role === 'superadmin' ? 'Super Admin' : 'Admin'
                        }
                        size="small"
                        color={
                          admin.role === 'superadmin' ? 'primary' : 'default'
                        }
                        icon={<SecurityIcon />}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={admin.isActive ? 'Active' : 'Inactive'}
                        size="small"
                        color={admin.isActive ? 'success' : 'error'}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {admin.lastLoginAt
                          ? formatDate(admin.lastLoginAt)
                          : 'Never'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatDate(admin.createdAt)}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Tooltip title="Reset Password">
                        <IconButton
                          size="small"
                          onClick={() => handleResetPassword(admin)}
                          disabled={admin.id === currentAdmin._id}
                        >
                          <KeyIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      ) : (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            Admin account management is restricted to superadmins only.
          </Typography>
        </Paper>
      )}

      {/* Reset Password Dialog */}
      <Dialog
        open={resetDialogOpen}
        onClose={handleResetDialogClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Reset Password for {selectedAdmin?.name}</DialogTitle>
        <DialogContent>
          {resetSuccess && (
            <Alert severity="success" sx={{ mb: 2 }}>
              {resetSuccess}
            </Alert>
          )}
          {resetError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {resetError}
            </Alert>
          )}
          <TextField
            fullWidth
            label="New Password"
            type={showPassword ? 'text' : 'password'}
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            margin="normal"
            required
            disabled={resetLoading || !!resetSuccess}
            helperText="Password must be at least 8 characters long"
            InputProps={{
              endAdornment: (
                <IconButton
                  aria-label="toggle password visibility"
                  onClick={() => setShowPassword(!showPassword)}
                  edge="end"
                  disabled={resetLoading || !!resetSuccess}
                >
                  {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                </IconButton>
              ),
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleResetDialogClose} disabled={resetLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmitPasswordReset}
            variant="contained"
            disabled={
              resetLoading ||
              !newPassword ||
              newPassword.length < 8 ||
              !!resetSuccess
            }
            startIcon={
              resetLoading ? <CircularProgress size={20} /> : <KeyIcon />
            }
          >
            {resetLoading ? 'Resetting...' : 'Reset Password'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create Admin Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={handleCreateDialogClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Create New Admin Account</DialogTitle>
        <DialogContent>
          {createSuccess && (
            <Alert severity="success" sx={{ mb: 2 }}>
              {createSuccess}
            </Alert>
          )}
          {createError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {createError}
            </Alert>
          )}
          <TextField
            fullWidth
            label="Name"
            value={newAdminData.name}
            onChange={(e) =>
              setNewAdminData({ ...newAdminData, name: e.target.value })
            }
            margin="normal"
            required
            disabled={createLoading || !!createSuccess}
          />
          <TextField
            fullWidth
            label="Email"
            type="email"
            value={newAdminData.email}
            onChange={(e) =>
              setNewAdminData({ ...newAdminData, email: e.target.value })
            }
            margin="normal"
            required
            disabled={createLoading || !!createSuccess}
          />
          <TextField
            fullWidth
            label="Password"
            type={showCreatePassword ? 'text' : 'password'}
            value={newAdminData.password}
            onChange={(e) =>
              setNewAdminData({ ...newAdminData, password: e.target.value })
            }
            margin="normal"
            required
            disabled={createLoading || !!createSuccess}
            helperText="Password must be at least 8 characters long"
            InputProps={{
              endAdornment: (
                <IconButton
                  aria-label="toggle password visibility"
                  onClick={() => setShowCreatePassword(!showCreatePassword)}
                  edge="end"
                  disabled={createLoading || !!createSuccess}
                >
                  {showCreatePassword ? (
                    <VisibilityOffIcon />
                  ) : (
                    <VisibilityIcon />
                  )}
                </IconButton>
              ),
            }}
          />
          <FormControl
            fullWidth
            margin="normal"
            disabled={createLoading || !!createSuccess}
          >
            <InputLabel>Role</InputLabel>
            <Select
              value={newAdminData.role}
              label="Role"
              onChange={(e) =>
                setNewAdminData({
                  ...newAdminData,
                  role: e.target.value as 'admin' | 'superadmin',
                })
              }
            >
              <MenuItem value="admin">Admin</MenuItem>
              <MenuItem value="superadmin">Super Admin</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCreateDialogClose} disabled={createLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmitCreateAdmin}
            variant="contained"
            disabled={
              createLoading ||
              !newAdminData.email ||
              !newAdminData.password ||
              !newAdminData.name ||
              newAdminData.password.length < 8 ||
              !!createSuccess
            }
            startIcon={
              createLoading ? <CircularProgress size={20} /> : <AddIcon />
            }
          >
            {createLoading ? 'Creating...' : 'Create Admin'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
