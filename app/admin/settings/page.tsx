import AdminSettingsClient from './admin-settings.client';
import { requireAdminAuth } from '../../../lib/api/admin-auth';

export default async function AdminSettings() {
  // Require admin authentication (not just superadmin)
  const admin = await requireAdminAuth();

  // Convert Mongoose document to plain object for client component
  const adminData = {
    _id: admin._id.toString(),
    email: admin.email,
    name: admin.name,
    role: admin.role,
    isActive: admin.isActive,
    lastLoginAt: admin.lastLoginAt,
    createdAt: admin.createdAt,
    updatedAt: admin.updatedAt,
  };

  return <AdminSettingsClient currentAdmin={adminData} />;
}
