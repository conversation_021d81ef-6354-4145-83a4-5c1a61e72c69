'use client';

import { ChangeEvent, useState, useRef } from 'react';
import { IWaveInput } from '../../../lib/models/wave';
import { ISurveyImport } from '../../../lib/models/survey';
import { addWave } from '../../../lib/api/waves';
import { parse, ParseResult } from 'papaparse';
import { expectedSurveyHeaders } from '../../_constants/expected-seed-headers';
import CircularProgress from '@mui/material/CircularProgress';
import {
  Box,
  Typography,
  TextField,
  Button,
  Input,
  Alert,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import DashboardPanel from '../dashboard/DashboardPanel';
import PanelHeader from '../dashboard/PanelHeader';
import PanelSubheader from '../dashboard/PanelSubheader';

interface NewWaveClientProps {
  pageTitle?: string;
}

const NewWaveClient = ({ pageTitle = 'Import Wave' }: NewWaveClientProps) => {
  const theme = useTheme();
  const [name, setName] = useState<string>('');
  const [importFilename, setImportFilename] = useState<string>('');
  const [surveyImports, setSurveyImports] = useState<ISurveyImport[]>([]);
  const [importError, setImportError] = useState<string>('');
  const [isCreating, setIsCreating] = useState<boolean>(false);
  const [createWaveError, setCreateWaveError] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const nameOnChange = (event: ChangeEvent<HTMLInputElement>) => {
    setName(event.target.value);
  };

  const clearSelectedFileOnClick = () => {
    setImportFilename('');
    setSurveyImports([]);
    setImportError('');
  };

  const createWaveOnClick = async () => {
    setCreateWaveError('');
    setIsCreating(true);
    const newWave: IWaveInput = { name };
    await addWave(newWave, surveyImports).catch((error) => {
      setCreateWaveError(error.message);
    });
    setIsCreating(false);
  };

  const processFile = (file: File): void => {
    setImportError('');
    setCreateWaveError('');

    const complete = async (results: ParseResult<string[]>): Promise<void> => {
      const headerLocations = expectedSurveyHeaders.map((header) =>
        results.data[0].indexOf(header)
      );

      if (headerLocations.includes(-1)) {
        setImportError(
          `CSV must contain headers: ${expectedSurveyHeaders.join(', ')}`
        );
        return;
      }

      const newSurveyImports: ISurveyImport[] = results.data
        .slice(1) // skip header row
        .filter((row) => row.some((cell) => cell.trim() !== '')) // filter out empty rows
        .map((row: string[]) => ({
          accountName: row[headerLocations[0]],
          agencyName: row[headerLocations[1]],
          agencyType: row[headerLocations[2]],
          brand: row[headerLocations[3]],
          country: row[headerLocations[4]],
          region: row[headerLocations[5]],
          assessmentType: row[headerLocations[6]],
          userName: row[headerLocations[7]],
          userEmail: row[headerLocations[8]],
          userStatus: row[headerLocations[9]],
          inScope: row[headerLocations[10]],
          notes: row[headerLocations[11]] || '',
        }));

      const hasEmptyField: boolean = newSurveyImports.some(
        (obj) =>
          !obj.accountName || !obj.agencyName || !obj.userEmail || !obj.userName
      );

      if (hasEmptyField) {
        setImportError(
          'CSV contains one or more rows with missing required fields (Account Name, Agency Name, User Name, User Email).'
        );
        return;
      }

      setImportFilename(file.name);
      setSurveyImports(newSurveyImports);
    };

    const error = (error: Error): void => {
      setImportError(`Parsing error: ${error.message}`);
    };

    parse<string[]>(file, { complete, error });
  };

  // Handle file input change
  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      processFile(e.target.files[0]);
    }
  };

  // Handle drag and drop
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      processFile(e.dataTransfer.files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  return (
    <Box sx={{ p: { xs: 1, md: 4 } }}>
      <Typography variant="h4" fontWeight={700} gutterBottom>
        {pageTitle}
      </Typography>
      <PanelSubheader gutterBottom sx={{ mb: 4 }}>
        Create a new wave and import surveys
      </PanelSubheader>

      <DashboardPanel sx={{ maxWidth: 600, mb: 4 }}>
        <PanelHeader gutterBottom>Wave Configuration</PanelHeader>

        <Box sx={{ mb: 3 }}>
          <PanelSubheader sx={{ mb: 1 }}>Wave Name</PanelSubheader>
          <TextField
            fullWidth
            size="small"
            label="Wave Name"
            value={name}
            onChange={nameOnChange}
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'background.paper',
                borderRadius: 1,
              },
            }}
          />
        </Box>

        <Box>
          <PanelSubheader gutterBottom>Select Surveys (CSV)</PanelSubheader>
          {importFilename ? (
            <Box sx={{ mb: 2 }}>
              <PanelSubheader sx={{ mb: 1 }}>
                Survey file selected.
              </PanelSubheader>
              <PanelSubheader sx={{ mb: 1 }}>
                {`${surveyImports.length} total surveys from ${importFilename}`}
              </PanelSubheader>
              <Button
                variant="outlined"
                color="secondary"
                onClick={clearSelectedFileOnClick}
              >
                Choose different file
              </Button>
            </Box>
          ) : (
            <Box
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              sx={{
                border: `2px dashed ${
                  theme.palette.mode === 'dark' ? '#06406F' : '#90caf9'
                }`,
                borderRadius: 2,
                p: 3,
                textAlign: 'center',
                mb: 2,
                bgcolor: theme.palette.mode === 'dark' ? '#020609' : '#f5fafd',
                cursor: 'pointer',
              }}
              onClick={() => fileInputRef.current?.click()}
            >
              <Input
                inputRef={fileInputRef}
                type="file"
                inputProps={{ accept: '.csv' }}
                onChange={handleFileChange}
                sx={{ display: 'none' }}
              />
              <PanelSubheader>
                Drag and drop a CSV file here, or click to browse
              </PanelSubheader>
            </Box>
          )}
          {importError && <Alert severity="error">{importError}</Alert>}
        </Box>
      </DashboardPanel>

      {createWaveError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {createWaveError}
        </Alert>
      )}

      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <Button
          variant="contained"
          color="primary"
          onClick={createWaveOnClick}
          disabled={!name || !surveyImports.length || isCreating}
        >
          Create Wave
        </Button>
        {isCreating && <CircularProgress size={24} />}
      </Box>
    </Box>
  );
};

export default NewWaveClient;
