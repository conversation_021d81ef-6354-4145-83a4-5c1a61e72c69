import { NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import { WaveModel } from '../../../../lib/models/wave';
import { SurveyModel } from '../../../../lib/models/survey';
import { UserPasscodeModel } from '../../../../lib/models/user-passcode';

export async function GET() {
  try {
    await dbConnect();

    // Get wave counts by status
    const waveStats = await WaveModel.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get recent waves
    const recentWaves = await WaveModel.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('name status createdAt')
      .lean();

    // Get survey counts by wave
    const surveyStats = await SurveyModel.aggregate([
      {
        $group: {
          _id: '$waveId',
          count: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: 'waves',
          localField: '_id',
          foreignField: '_id',
          as: 'wave'
        }
      },
      { $unwind: '$wave' },
      {
        $project: {
          waveId: '$_id',
          waveName: '$wave.name',
          waveStatus: '$wave.status',
          surveyCount: '$count'
        }
      },
      { $sort: { surveyCount: -1 } },
      { $limit: 10 }
    ]);

    // Get passcode counts by wave
    const passcodeStats = await UserPasscodeModel.aggregate([
      {
        $group: {
          _id: '$waveId',
          total: { $sum: 1 },
          used: { $sum: { $cond: [{ $ifNull: ['$usedAt', false] }, 1, 0] } },
          unused: { $sum: { $cond: [{ $ifNull: ['$usedAt', false] }, 0, 1] } }
        }
      },
      {
        $lookup: {
          from: 'waves',
          localField: '_id',
          foreignField: '_id',
          as: 'wave'
        }
      },
      { $unwind: '$wave' },
      {
        $project: {
          waveId: '$_id',
          waveName: '$wave.name',
          waveStatus: '$wave.status',
          totalPasscodes: '$total',
          usedPasscodes: '$used',
          unusedPasscodes: '$unused'
        }
      },
      { $sort: { totalPasscodes: -1 } },
      { $limit: 10 }
    ]);

    return NextResponse.json({
      success: true,
      overview: {
        waves: {
          byStatus: waveStats,
          recent: recentWaves
        },
        surveys: {
          byWave: surveyStats,
          total: await SurveyModel.countDocuments()
        },
        passcodes: {
          byWave: passcodeStats,
          total: await UserPasscodeModel.countDocuments()
        }
      }
    });

  } catch (error) {
    console.error('Database overview error:', error);
    return NextResponse.json(
      { error: 'Failed to get database overview' },
      { status: 500 }
    );
  }
} 