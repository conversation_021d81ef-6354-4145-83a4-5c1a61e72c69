import { NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import { UserPasscodeModel } from '../../../../lib/models/user-passcode';
import { SurveyModel } from '../../../../lib/models/survey';

interface TestDataItem {
  email: string;
  passcode: string;
  waveId: string;
  surveyCount: number;
  debugUrl: string;
}

export async function GET() {
  try {
    await dbConnect();

    // Get the first 5 user passcodes for testing
    const testData = await UserPasscodeModel.find({
      usedAt: { $exists: false } // Only show unused passcodes
    })
      .limit(5)
      .select('userEmail passcode waveId')
      .lean();

    // For each test item, check if there are surveys
    const enhancedTestData: TestDataItem[] = await Promise.all(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      testData.map(async (item: any): Promise<TestDataItem> => {
        const surveyCount = await SurveyModel.countDocuments({
          userEmail: item.userEmail,
          waveId: item.waveId
        });

        return {
          email: item.userEmail,
          passcode: item.passcode,
          waveId: item.waveId.toString(),
          surveyCount,
          debugUrl: `/api/auth/debug-user?passcode=${item.passcode}`
        };
      })
    );

    return NextResponse.json({
      success: true,
      testData: enhancedTestData
    });
  } catch (error) {
    console.error('Error fetching test data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch test data' },
      { status: 500 }
    );
  }
} 