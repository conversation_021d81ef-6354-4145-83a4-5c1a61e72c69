import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import { UserPasscodeModel } from '../../../../lib/models/user-passcode';
import { SurveyModel } from '../../../../lib/models/survey';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    const { searchParams } = new URL(request.url);
    const passcode = searchParams.get('passcode');

    if (!passcode) {
      return NextResponse.json(
        { error: 'Passcode parameter required' },
        { status: 400 }
      );
    }

    // Find the user passcode record
    const userPasscode = await UserPasscodeModel.findOne({ 
      passcode: passcode.trim()
    });

    if (!userPasscode) {
      return NextResponse.json(
        { error: 'Passcode not found' },
        { status: 404 }
      );
    }

    // Get all surveys for this user email
    const surveysByEmail = await SurveyModel.find({ 
      userEmail: userPasscode.userEmail 
    }).select('userEmail waveId accountName agencyName');

    // Get all surveys for this wave
    const surveysByWave = await SurveyModel.find({ 
      waveId: userPasscode.waveId 
    }).select('userEmail waveId accountName agencyName').limit(5);

    // Get surveys matching both criteria
    const surveysByBoth = await SurveyModel.find({ 
      userEmail: userPasscode.userEmail,
      waveId: userPasscode.waveId
    }).select('userEmail waveId accountName agencyName');

    return NextResponse.json({
      success: true,
      debug: {
        passcode: userPasscode.passcode,
        userEmail: userPasscode.userEmail,
        waveId: userPasscode.waveId.toString(),
        counts: {
          surveysByEmail: surveysByEmail.length,
          surveysByWave: surveysByWave.length,
          surveysByBoth: surveysByBoth.length
        },
        samples: {
          surveysByEmail: surveysByEmail.slice(0, 3),
          surveysByWave: surveysByWave.slice(0, 3),
          surveysByBoth: surveysByBoth
        }
      }
    });

  } catch (error) {
    console.error('Debug error:', error);
    return NextResponse.json(
      { error: 'Debug failed' },
      { status: 500 }
    );
  }
} 