import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '../../../../lib/api/auth';

export async function POST(request: NextRequest) {
  try {
    const { email, passcode } = await request.json();

    // Validate required fields
    if (!email || typeof email !== 'string' || !email.trim()) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    if (!passcode || typeof passcode !== 'string' || !passcode.trim()) {
      return NextResponse.json(
        { error: 'Passcode is required' },
        { status: 400 }
      );
    }

    // Authenticate user with email and passcode
    const result = await authenticateUser(email.trim(), passcode.trim());

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 401 }
      );
    }

    return NextResponse.json({
      success: true,
      user: result.user,
      surveys: result.surveys,
      passcode: result.passcode,
    });

  } catch (error) {
    console.error('Authentication error:', error);
    
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 500 }
    );
  }
} 