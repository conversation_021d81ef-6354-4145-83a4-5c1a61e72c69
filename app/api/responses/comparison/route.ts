import { NextRequest, NextResponse } from 'next/server';
import { getQuestionAnalytics } from '../../../../lib/api/responses';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Get items to compare (comma-separated) and focus type
    const itemsParam = searchParams.get('brands'); // Keep same param name for backwards compatibility
    const focus = searchParams.get('focus') || 'brand';
    const agencyName = searchParams.get('agencyName');
    const region = searchParams.get('region');
    const wave = searchParams.get('wave');
    const respondentType = searchParams.get('respondentType');
    
    if (!itemsParam || !wave) {
      return NextResponse.json(
        { error: 'brands and wave parameters are required' },
        { status: 400 }
      );
    }
    
    const items = itemsParam.split(',').map(b => b.trim());
    
    // Build base filters
    const baseFilters: Record<string, string> = {};
    
    // Set fixed filters based on focus type
    if (focus === 'wave') {
      // When comparing waves, fix agency and region, vary wave
      if (agencyName && agencyName !== 'All Agencies') {
        baseFilters.agencyName = agencyName;
      }
      if (region && region !== 'All Regions') {
        baseFilters.region = region;
      }
    } else if (focus === 'agency') {
      // When comparing agencies, fix wave and region, vary agency
      baseFilters.wave = wave;
      if (region && region !== 'All Regions') {
        baseFilters.region = region;
      }
    } else {
      // When comparing brands, fix wave, agency, and region, vary brand
      baseFilters.wave = wave;
      if (agencyName && agencyName !== 'All Agencies') {
        baseFilters.agencyName = agencyName;
      }
      if (region && region !== 'All Regions') {
        baseFilters.region = region;
      }
    }
    
    // Map respondent type to assessment type (only if not "All")
    if (respondentType === 'ABI on Agency') {
      baseFilters.assessmentType = 'Anheuser Busch In-Bev-on-Agency';
    } else if (respondentType === 'Agency on ABI') {
      baseFilters.assessmentType = 'Agency-on-Anheuser Busch In-Bev';
    }
    
    // Fetch analytics for each item
    const itemAnalytics = await Promise.all(
      items.map(async (item) => {
        const filters = { ...baseFilters };
        
        // Set the varying filter based on focus type
        if (focus === 'wave') {
          filters.wave = item;
        } else if (focus === 'agency') {
          filters.agencyName = item;
        } else {
          filters.brand = item;
        }
        
        const analytics = await getQuestionAnalytics(filters);
        return {
          item,
          q1Avg: analytics.q1Avg,
          q2Avg: analytics.q2Avg,
          q3Avg: analytics.q3Avg,
          q4Avg: analytics.q4Avg,
          q5Avg: analytics.q5Avg,
          responseCount: analytics.responseCount,
        };
      })
    );
    
    // Structure data for charts and tables
    const categories = [
      'People and Partnership',
      'Account Management and Process', 
      'Strategy',
      'Creativity',
      'Media Planning and Execution',
    ];
    
    const comparisonData = items.map(item => {
      const itemData = itemAnalytics.find(b => b.item === item);
      return {
        brand: item, // Keep same property name for backwards compatibility
        data: itemData ? [
          itemData.q1Avg,
          itemData.q2Avg,
          itemData.q3Avg,
          itemData.q4Avg,
          itemData.q5Avg,
        ] : [0, 0, 0, 0, 0],
        responseCount: itemData?.responseCount || 0,
      };
    });
    
    return NextResponse.json({
      categories,
      brands: comparisonData, // Keep same property name for backwards compatibility
      summary: itemAnalytics.map(item => ({
        brand: item.item, // Keep same property name for backwards compatibility
        q1Avg: item.q1Avg,
        q2Avg: item.q2Avg,
        q3Avg: item.q3Avg,
        q4Avg: item.q4Avg,
        q5Avg: item.q5Avg,
        responseCount: item.responseCount,
      })),
    });
  } catch (error) {
    console.error('Error fetching comparison data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch comparison data' },
      { status: 500 }
    );
  }
} 