import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import { ResponseModel } from '../../../../lib/models/response';
import { SurveyModel } from '../../../../lib/models/survey';

export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    const {
      surveyId,
      waveId,
      answers,
      userEmail,
      userName,
    } = await request.json();

    if (
      !surveyId ||
      !waveId ||
      !answers ||
      !Array.isArray(answers) ||
      !userEmail ||
      !userName
    ) {
      return NextResponse.json(
        {
          error:
            'Missing required information. Please refresh the page and try again.',
        },
        { status: 400 }
      );
    }

    // Check if response already exists
    const existingResponse = await ResponseModel.findOne({
      surveyId,
      userEmail,
    });

    if (!existingResponse) {
      return NextResponse.json(
        {
          error: 'No survey progress found. Please start the survey again.',
        },
        { status: 404 }
      );
    }

    // Check if the survey has already been marked as submitted
    const survey = await SurveyModel.findById(surveyId);
    if (survey && survey.userStatus === 'Submitted') {
      return NextResponse.json(
        {
          error: 'Survey has already been submitted.',
        },
        { status: 409 }
      );
    }

    // Prepare final response data with derived fields
    const updateData: any = {
      assessorName: userName,
      assessorType: 'User', // Default type for user submissions
    };

    // Ensure all answers are properly saved (in case this is called without saving all progress)
    answers.forEach((answer: { questionId: string; value: string; comment: string }) => {
      const questionNum = answer.questionId.replace('q', '');
      updateData[`q${questionNum}Score`] = parseInt(answer.value, 10);
      updateData[`q${questionNum}Comment`] = answer.comment;
    });

    // Calculate derived fields from the existing response + new data
    const allScores = [
      updateData.q1Score || existingResponse.q1Score,
      updateData.q2Score || existingResponse.q2Score,
      updateData.q3Score || existingResponse.q3Score,
      updateData.q4Score || existingResponse.q4Score,
      updateData.q5Score || existingResponse.q5Score,
    ].filter((score): score is number => score !== undefined);

    // Calculate NPS from q1 if available
    const q1Score = updateData.q1Score || existingResponse.q1Score;
    if (q1Score) {
      updateData.npsScore = q1Score >= 4 ? 100 : q1Score >= 3 ? 50 : 0;
    }

    // Calculate overall rating
    if (allScores.length > 0) {
      updateData.overallRating = allScores.reduce((sum, score) => sum + score, 0) / allScores.length;
    }

    // Update the existing response with final data
    await ResponseModel.findOneAndUpdate(
      { surveyId, userEmail },
      { $set: updateData },
      { new: true }
    );

    // Update survey status to completed
    await SurveyModel.findByIdAndUpdate(surveyId, {
      userStatus: 'Submitted'
    });

    return NextResponse.json({ 
      success: true,
      message: 'Survey submitted successfully'
    });

  } catch (error) {
    console.error('Error submitting survey:', error);
    return NextResponse.json(
      {
        error: 'Failed to submit survey. Please try again.',
      },
      { status: 500 }
    );
  }
} 