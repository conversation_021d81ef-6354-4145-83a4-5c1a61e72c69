import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import { ResponseModel } from '../../../../lib/models/response';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    
    const { searchParams } = new URL(request.url);
    const surveyId = searchParams.get('surveyId');
    const userEmail = searchParams.get('userEmail');

    if (!surveyId || !userEmail) {
      return NextResponse.json({ 
        error: 'Missing surveyId or userEmail parameter' 
      }, { status: 400 });
    }

    // Find existing response for this survey and user
    const existingResponse = await ResponseModel.findOne({
      surveyId,
      userEmail,
    });

    if (!existingResponse) {
      return NextResponse.json({ 
        answers: {},
        isComplete: false
      });
    }

    // Extract question answers from the response
    const answers: { [key: string]: string | number } = {};
    let completedQuestions = 0;
    const totalQuestions = 5; // Based on survey questions

    for (let i = 1; i <= totalQuestions; i++) {
      const scoreField = `q${i}Score`;
      const commentField = `q${i}Comment`;
      
      if (existingResponse[scoreField] !== undefined && existingResponse[commentField] !== undefined) {
        answers[`q${i}`] = existingResponse[scoreField].toString();
        answers[`q${i}-comment`] = existingResponse[commentField] || '';
        completedQuestions++;
      }
    }

    const isComplete = completedQuestions === totalQuestions;

    return NextResponse.json({ 
      answers,
      isComplete,
      completedQuestions,
      totalQuestions
    });

  } catch (error) {
    console.error('Error fetching progress:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch survey progress' 
    }, { status: 500 });
  }
} 