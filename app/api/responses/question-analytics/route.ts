import { NextRequest, NextResponse } from 'next/server';
import { getQuestionAnalytics } from '../../../../lib/api/responses';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const filters: Record<string, string> = {};
    
    // Extract filter parameters
    const agencyName = searchParams.get('agencyName');
    const brand = searchParams.get('brand');
    const region = searchParams.get('region');
    const assessmentType = searchParams.get('assessmentType');
    const wave = searchParams.get('wave');
    
    if (agencyName) filters.agencyName = agencyName;
    if (brand) filters.brand = brand;
    if (region) filters.region = region;
    if (assessmentType) filters.assessmentType = assessmentType;
    if (wave) filters.wave = wave;
    
    const analytics = await getQuestionAnalytics(filters);
    return NextResponse.json(analytics);
  } catch (error) {
    console.error('Error fetching question analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch question analytics' },
      { status: 500 }
    );
  }
} 