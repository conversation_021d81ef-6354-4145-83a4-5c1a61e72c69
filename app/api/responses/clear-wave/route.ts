import { NextRequest, NextResponse } from 'next/server';
import { clearResponsesForWave } from '../../../../lib/api/responses';

export async function POST(request: NextRequest) {
  try {
    const { waveName } = await request.json();
    
    if (!waveName) {
      return NextResponse.json(
        { error: 'Wave name is required' },
        { status: 400 }
      );
    }

    const result = await clearResponsesForWave(waveName);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error clearing wave data:', error);
    return NextResponse.json(
      { error: 'Failed to clear wave data' },
      { status: 500 }
    );
  }
} 