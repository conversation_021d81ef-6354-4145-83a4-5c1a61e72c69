import { NextRequest, NextResponse } from 'next/server';
import { getResponseAnalytics } from '../../../../lib/api/responses';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const filters: Record<string, string> = {};
    
    // Extract filter parameters
    const brand = searchParams.get('brand');
    const region = searchParams.get('region');
    const assessmentType = searchParams.get('assessmentType');
    
    if (brand) filters.brand = brand;
    if (region) filters.region = region;
    if (assessmentType) filters.assessmentType = assessmentType;
    
    const analytics = await getResponseAnalytics(filters);
    return NextResponse.json(analytics);
  } catch (error) {
    console.error('Error fetching response analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch response analytics' },
      { status: 500 }
    );
  }
} 