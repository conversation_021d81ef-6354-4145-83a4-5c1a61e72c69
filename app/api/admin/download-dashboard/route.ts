import { NextRequest, NextResponse } from 'next/server';
import { generateDashboardPDF } from '../../../../lib/pdf-generator';
import { generateDashboardPowerPoint } from '../../../../lib/powerpoint-generator';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format');
    const filename = searchParams.get('filename') || 'Echo360-Dashboard';
    
    // Extract filter parameters
    const waves = searchParams.get('waves')?.split(',') || [];
    const agency = searchParams.get('agency') || 'All Agencies';
    const brand = searchParams.get('brand') || 'All Brands';
    const region = searchParams.get('region') || 'All Regions';
    
    const filters = {
      waves,
      agency,
      brand,
      region,
    };

    if (format === 'pdf') {
      const pdfBuffer = await generateDashboardPDF(filters, filename);
      
      return new NextResponse(pdfBuffer, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="${filename}.pdf"`,
        },
      });
    } else if (format === 'powerpoint') {
      const pptxBuffer = await generateDashboardPowerPoint(filters);
      
      return new NextResponse(pptxBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          'Content-Disposition': `attachment; filename="${filename}.pptx"`,
        },
      });
    } else {
      return NextResponse.json(
        { error: 'Invalid format. Use "pdf" or "powerpoint".' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Download generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate download' },
      { status: 500 }
    );
  }
}
