import { NextRequest, NextResponse } from 'next/server';
import { createAdmin, requireSuperAdminAuth } from '../../../../../lib/api/admin-auth';

export async function POST(request: NextRequest) {
  try {
    // Verify superadmin authentication
    const requestingAdmin = await requireSuperAdminAuth();

    const { email, password, name, role } = await request.json();

    // Validate required fields
    if (!email || typeof email !== 'string') {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    if (!password || typeof password !== 'string' || password.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    if (!name || typeof name !== 'string') {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    if (!role || !['admin', 'superadmin'].includes(role)) {
      return NextResponse.json(
        { error: 'Role must be either "admin" or "superadmin"' },
        { status: 400 }
      );
    }

    // Create admin
    const result = await createAdmin(
      email,
      password,
      name,
      role as 'admin' | 'superadmin',
      requestingAdmin._id.toString()
    );

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Admin account created successfully',
      admin: result.admin
    });

  } catch (error) {
    console.error('Create admin error:', error);
    
    if (error instanceof Error && error.message === 'Superadmin access required') {
      return NextResponse.json(
        { error: 'Unauthorized: Superadmin access required' },
        { status: 403 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to create admin account' },
      { status: 500 }
    );
  }
} 