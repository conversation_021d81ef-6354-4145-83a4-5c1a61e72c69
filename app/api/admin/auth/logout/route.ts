import { NextResponse } from 'next/server';
import { logoutAdmin } from '../../../../../lib/api/admin-auth';

export async function POST() {
  try {
    await logoutAdmin();

    return NextResponse.json({
      success: true,
    });

  } catch (error) {
    console.error('Admin logout error:', error);
    
    return NextResponse.json(
      { error: 'Logout failed' },
      { status: 500 }
    );
  }
}