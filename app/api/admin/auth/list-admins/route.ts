import { NextResponse } from 'next/server';
import { getAllAdmins, requireSuperAdminAuth } from '../../../../../lib/api/admin-auth';

export async function GET() {
  try {
    // Verify superadmin authentication
    const requestingAdmin = await requireSuperAdminAuth();

    // Get all admins
    const result = await getAllAdmins(requestingAdmin._id.toString());

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      admins: result.admins
    });

  } catch (error) {
    console.error('List admins error:', error);
    
    if (error instanceof Error && error.message === 'Superadmin access required') {
      return NextResponse.json(
        { error: 'Unauthorized: Superadmin access required' },
        { status: 403 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to retrieve admins' },
      { status: 500 }
    );
  }
}