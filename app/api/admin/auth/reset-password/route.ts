import { NextRequest, NextResponse } from 'next/server';
import { resetAdminPassword, requireSuperAdminAuth } from '../../../../../lib/api/admin-auth';

export async function POST(request: NextRequest) {
  try {
    // Verify superadmin authentication
    const requestingAdmin = await requireSuperAdminAuth();

    const { adminId, newPassword } = await request.json();

    // Validate required fields
    if (!adminId || typeof adminId !== 'string') {
      return NextResponse.json(
        { error: 'Admin ID is required' },
        { status: 400 }
      );
    }

    if (!newPassword || typeof newPassword !== 'string' || newPassword.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Reset password
    const result = await resetAdminPassword(
      adminId,
      newPassword,
      requestingAdmin._id.toString()
    );

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Password reset successfully'
    });

  } catch (error) {
    console.error('Password reset error:', error);
    
    if (error instanceof Error && error.message === 'Superadmin access required') {
      return NextResponse.json(
        { error: 'Unauthorized: Superadmin access required' },
        { status: 403 }
      );
    }
    
    return NextResponse.json(
      { error: 'Password reset failed' },
      { status: 500 }
    );
  }
}