import { NextResponse } from 'next/server';
import { getAdminSession } from '../../../../../lib/api/admin-auth';

export async function GET() {
  try {
    const session = await getAdminSession();

    return NextResponse.json(session);

  } catch (error) {
    console.error('Admin session check error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Session check failed' },
      { status: 500 }
    );
  }
}