import { NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import { WaveModel } from '../../../../lib/models/wave';
import { UserPasscodeModel } from '../../../../lib/models/user-passcode';

export async function POST() {
  try {
    await dbConnect();

    // Get all seed wave IDs
    const seedWaves = await WaveModel.find({ status: 'seed' }).select('_id');
    const seedWaveIds = seedWaves.map(wave => wave._id);

    if (seedWaveIds.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No seed waves found',
        deletedCount: 0,
      });
    }

    // Delete any passcodes associated with seed waves
    const deleteResult = await UserPasscodeModel.deleteMany({
      waveId: { $in: seedWaveIds }
    });

    return NextResponse.json({
      success: true,
      message: `Cleaned up ${deleteResult.deletedCount} incorrect passcodes from ${seedWaveIds.length} seed waves`,
      deletedCount: deleteResult.deletedCount,
      seedWavesChecked: seedWaveIds.length,
    });

  } catch (error) {
    console.error('Error cleaning up seed passcodes:', error);
    return NextResponse.json(
      { error: 'Failed to clean up seed passcodes' },
      { status: 500 }
    );
  }
} 