import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import { WaveModel, IWaveConfig } from '../../../../lib/models/wave';

export async function POST(request: NextRequest) {
  try {
    await dbConnect();

    const body = await request.json();
    const { 
      name, 
      selectedRegions, 
      selectedAgencyTypes, 
      selectedBrands, 
      selectedAgencies, 
      selectedPeriod,
      selectedYear 
    } = body;

    // Validate required fields
    if (!name || typeof name !== 'string' || !name.trim()) {
      return NextResponse.json(
        { error: 'Wave name is required' },
        { status: 400 }
      );
    }

    // Create config object
    const config: IWaveConfig = {
      selectedRegions: selectedRegions || [],
      selectedAgencyTypes: selectedAgencyTypes || [],
      selectedBrands: selectedBrands || [],
      selectedAgencies: selectedAgencies || [],
      selectedPeriod: selectedPeriod || 'FY',
      selectedYear: selectedYear || new Date().getFullYear(),
    };

    // Check if wave with this name already exists
    const existingWave = await WaveModel.findOne({ name: name.trim() });
    
    if (existingWave) {
      // Update existing draft
      if (existingWave.status !== 'draft') {
        return NextResponse.json(
          { error: 'Cannot update a wave that is not in draft status' },
          { status: 400 }
        );
      }

      existingWave.config = config;
      await existingWave.save();

      return NextResponse.json({
        success: true,
        message: 'Draft updated successfully',
        waveId: existingWave._id.toString(),
      });
    } else {
      // Create new draft
      const newWave = await WaveModel.create({
        name: name.trim(),
        status: 'draft',
        config,
      });

      return NextResponse.json({
        success: true,
        message: 'Draft saved successfully',
        waveId: newWave._id.toString(),
      });
    }
  } catch (error) {
    console.error('Error saving wave draft:', error);
    
    // Handle unique constraint violation (duplicate name)
    if (error instanceof Error && error.message.includes('duplicate key')) {
      return NextResponse.json(
        { error: 'A wave with this name already exists' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to save wave draft' },
      { status: 500 }
    );
  }
} 