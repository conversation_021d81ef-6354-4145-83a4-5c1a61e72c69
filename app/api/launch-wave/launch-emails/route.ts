import { NextRequest, NextResponse } from 'next/server';
import { Types } from 'mongoose';
import dbConnect from '../../../../lib/mongodb';
import { WaveModel, IWaveConfig } from '../../../../lib/models/wave';
import { SurveyModel } from '../../../../lib/models/survey';
import { UserPasscodeModel } from '../../../../lib/models/user-passcode';
import generatePasscode from '../../../../util/generate-passcode';

export async function POST(request: NextRequest) {
  try {
    await dbConnect();

    const body = await request.json();
    const { 
      waveId,
      selectedSeedWaveId,
      selectedRegions, 
      selectedAgencyTypes, 
      selectedBrands, 
      selectedAgencies, 
      selectedPeriod,
      selectedYear 
    } = body;

    let wave;
    
    if (waveId) {
      // Updating existing wave
      wave = await WaveModel.findById(waveId);
      
      if (!wave) {
        return NextResponse.json(
          { error: 'Wave not found' },
          { status: 404 }
        );
      }
      
      if (wave.status !== 'draft') {
        return NextResponse.json(
          { error: 'Only draft waves can be launched' },
          { status: 400 }
        );
      }
    } else {
      // This shouldn't happen if UI is working correctly, but handle it
      return NextResponse.json(
        { error: 'Wave ID is required for launching emails' },
        { status: 400 }
      );
    }

    // Get the selected seed wave to use as template
    let seedWave;
    
    if (selectedSeedWaveId) {
      seedWave = await WaveModel.findOne({ 
        _id: Types.ObjectId.createFromHexString(selectedSeedWaveId),
        status: 'seed' 
      });
      
      if (!seedWave) {
        return NextResponse.json(
          { error: 'Selected seed wave not found' },
          { status: 400 }
        );
      }
    } else {
      // Fallback to latest seed wave if no specific one selected
      seedWave = await WaveModel.findOne({ status: 'seed' }).sort({ createdAt: -1 });
      
      if (!seedWave) {
        return NextResponse.json(
          { error: 'No seed data available' },
          { status: 400 }
        );
      }
    }

    // Get all seed surveys that match our selections
    const matchingSeedSurveys = await SurveyModel.find({
      waveId: seedWave._id,
      country: { $in: selectedRegions },
      agencyType: { $in: selectedAgencyTypes },
      brand: { $in: selectedBrands },
      agencyName: { $in: selectedAgencies },
    });

    if (matchingSeedSurveys.length === 0) {
      return NextResponse.json(
        { error: 'No surveys match the selected criteria' },
        { status: 400 }
      );
    }

    // Create actual surveys for this wave
    const surveysToCreate = matchingSeedSurveys.map(seedSurvey => ({
      waveId: wave._id,
      accountName: seedSurvey.accountName,
      agencyName: seedSurvey.agencyName,
      agencyType: seedSurvey.agencyType,
      brand: seedSurvey.brand,
      country: seedSurvey.country,
      region: seedSurvey.region,
      assessmentType: seedSurvey.assessmentType,
      userName: seedSurvey.userName,
      userEmail: seedSurvey.userEmail,
      userStatus: 'Pending', // Reset status for new wave
      inScope: seedSurvey.inScope,
      notes: '',
    }));

    // Insert all surveys
    const createdSurveys = await SurveyModel.insertMany(surveysToCreate);

    // Generate unique passcodes for each unique user email
    const uniqueEmails = [...new Set(createdSurveys.map(s => s.userEmail))].filter(email => email && email.trim());
    
    const passcodesToCreate = [];
    for (const email of uniqueEmails) {
      let passcode;
      let isUnique = false;
      
      // Generate unique passcode
      while (!isUnique) {
        passcode = generatePasscode();
        const existingPasscode = await UserPasscodeModel.findOne({ passcode });
        if (!existingPasscode) {
          isUnique = true;
        }
      }
      
      passcodesToCreate.push({
        userEmail: email,
        passcode: passcode!,
        waveId: wave._id,
        expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
        isActive: true,
      });
    }

    // Insert passcodes
    await UserPasscodeModel.insertMany(passcodesToCreate);

    // Update wave status and config
    const config: IWaveConfig = {
      selectedRegions: selectedRegions || [],
      selectedAgencyTypes: selectedAgencyTypes || [],
      selectedBrands: selectedBrands || [],
      selectedAgencies: selectedAgencies || [],
      selectedPeriod: selectedPeriod || 'FY',
      selectedYear: selectedYear || new Date().getFullYear(),
    };

    wave.status = 'launched';
    wave.config = config;
    await wave.save();

    // TODO: Send actual emails here
    // For now, we'll just simulate email sending
    console.log(`Would send emails to ${uniqueEmails.length} unique recipients for wave: ${wave.name}`);

    return NextResponse.json({
      success: true,
      message: `Wave launched successfully! Created ${createdSurveys.length} surveys and ${passcodesToCreate.length} user accounts.`,
      stats: {
        surveysCreated: createdSurveys.length,
        userAccountsCreated: passcodesToCreate.length,
        uniqueEmails: uniqueEmails.length,
      },
    });
  } catch (error) {
    console.error('Error launching wave:', error);
    
    return NextResponse.json(
      { error: 'Failed to launch wave' },
      { status: 500 }
    );
  }
} 