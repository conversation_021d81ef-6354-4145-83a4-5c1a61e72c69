import { NextResponse } from 'next/server';
import { Types } from 'mongoose';
import dbConnect from '../../../../lib/mongodb';
import { SurveyModel } from '../../../../lib/models/survey';
import { WaveModel } from '../../../../lib/models/wave';

export async function GET(request: Request) {
  try {
    await dbConnect();

    const { searchParams } = new URL(request.url);
    const seedWaveId = searchParams.get('seedWaveId');

    let seedWave;
    
    if (seedWaveId) {
      // Get specific seed wave
      seedWave = await WaveModel.findOne({ 
        _id: Types.ObjectId.createFromHexString(seedWaveId),
        status: 'seed' 
      });
      
      if (!seedWave) {
        return NextResponse.json({
          hasData: false,
          message: 'Specified seed wave not found.',
        });
      }
    } else {
      // Get the latest seed wave (default behavior)
      seedWave = await WaveModel.findOne({ status: 'seed' }).sort({ createdAt: -1 });

      if (!seedWave) {
        return NextResponse.json({
          hasData: false,
          message: 'No seed data available. Please import seed data first.',
        });
      }
    }

    // Get all unique values from surveys in the selected seed wave
    const [regions, agencyTypes, brands, agencies] = await Promise.all([
      SurveyModel.distinct('country', { waveId: seedWave._id }),
      SurveyModel.distinct('agencyType', { waveId: seedWave._id }),
      SurveyModel.distinct('brand', { waveId: seedWave._id }),
      SurveyModel.distinct('agencyName', { waveId: seedWave._id }),
    ]);

    // Get survey counts for statistics
    const [totalSurveys, agencyRespondents, abiRespondents] = await Promise.all([
      SurveyModel.countDocuments({ waveId: seedWave._id }),
      SurveyModel.countDocuments({ 
        waveId: seedWave._id, 
        assessmentType: 'Agency-on-Anheuser Busch In-Bev' 
      }),
      SurveyModel.countDocuments({ 
        waveId: seedWave._id, 
        assessmentType: 'Anheuser Busch In-Bev-on-Agency' 
      }),
    ]);

    return NextResponse.json({
      hasData: true,
      waveInfo: {
        id: seedWave._id.toString(),
        name: seedWave.name,
        createdAt: seedWave.createdAt,
      },
      data: {
        regions: regions.sort(),
        agencyTypes: agencyTypes.sort(),
        brands: brands.sort(),
        agencies: agencies.sort(),
      },
      stats: {
        totalSurveys,
        agencyRespondents,
        abiRespondents,
      },
    });
  } catch (error) {
    console.error('Error fetching seed data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch seed data' },
      { status: 500 }
    );
  }
} 