import { NextResponse } from 'next/server';
import { getUniqueAgencies } from '../../../../lib/api/surveys';

export async function GET() {
  try {
    const agencies = await getUniqueAgencies();
    return NextResponse.json(agencies);
  } catch (error) {
    console.error('Error fetching unique agencies:', error);
    return NextResponse.json(
      { error: 'Failed to fetch agencies' },
      { status: 500 }
    );
  }
} 