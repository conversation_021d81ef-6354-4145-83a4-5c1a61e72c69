import { NextResponse } from 'next/server';
import { getUniqueRegions } from '../../../../lib/api/surveys';

export async function GET() {
  try {
    const regions = await getUniqueRegions();
    return NextResponse.json(regions);
  } catch (error) {
    console.error('Error fetching unique regions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch regions' },
      { status: 500 }
    );
  }
} 