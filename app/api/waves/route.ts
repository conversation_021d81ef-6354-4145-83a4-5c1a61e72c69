import { NextResponse } from 'next/server';
import { getWaveVms, getDashboardWaveVms } from '../../../lib/api/waves';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const excludeSeed = searchParams.get('excludeSeed') === 'true';
    
    const waves = excludeSeed ? await getDashboardWaveVms() : await getWaveVms();
    return NextResponse.json(waves);
  } catch (error) {
    console.error('Error fetching waves:', error);
    return NextResponse.json(
      { error: 'Failed to fetch waves' },
      { status: 500 }
    );
  }
} 