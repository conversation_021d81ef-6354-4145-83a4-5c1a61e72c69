import { NextResponse } from 'next/server';
import { getSeedWaveVms } from '../../../../lib/api/waves';

export async function GET() {
  try {
    const seedWaves = await getSeedWaveVms();
    return NextResponse.json(seedWaves);
  } catch (error) {
    console.error('Error fetching seed waves:', error);
    return NextResponse.json(
      { error: 'Failed to fetch seed waves' },
      { status: 500 }
    );
  }
} 