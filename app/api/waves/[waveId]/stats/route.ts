import { NextRequest, NextResponse } from 'next/server';
import { Types } from 'mongoose';
import dbConnect from '../../../../../lib/mongodb';
import { ResponseModel } from '../../../../../lib/models/response';
import { SurveyModel } from '../../../../../lib/models/survey';
import { WaveModel } from '../../../../../lib/models/wave';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ waveId: string }> }
) {
  try {
    await dbConnect();

    const { waveId } = await params;
    const waveObjectId = Types.ObjectId.createFromHexString(waveId);

    // First, get the wave to check its status
    const wave = await WaveModel.findById(waveObjectId);
    if (!wave) {
      return NextResponse.json(
        { error: 'Wave not found' },
        { status: 404 }
      );
    }

    // Handle seed waves differently - use survey data instead of response data
    if (wave.status === 'seed') {
      // Get survey statistics for seed waves
      const surveyStats = await SurveyModel.aggregate([
        {
          $match: {
            waveId: waveObjectId
          }
        },
        {
          $group: {
            _id: null,
            totalSurveys: { $sum: 1 },
            agencies: { $addToSet: '$agencyName' },
            brands: { $addToSet: '$brand' },
            regions: { $addToSet: '$country' },
            assessmentTypes: { $addToSet: '$assessmentType' },
            agencyTypes: { $addToSet: '$agencyType' }
          }
        }
      ]);

      // Get agency-level details for seed waves (based on surveys)
      const agencyDetails = await SurveyModel.aggregate([
        {
          $match: {
            waveId: waveObjectId
          }
        },
        {
          $group: {
            _id: '$agencyName',
            surveyCount: { $sum: 1 },
            brands: { $addToSet: '$brand' },
            regions: { $addToSet: '$country' },
            agencyTypes: { $addToSet: '$agencyType' }
          }
        },
        {
          $project: {
            agencyName: '$_id',
            surveyCount: 1,
            brandCount: { $size: '$brands' },
            regionCount: { $size: '$regions' },
            brands: 1,
            regions: 1,
            agencyTypes: 1
          }
        },
        { $sort: { agencyName: 1 } }
      ]);

      const stats = surveyStats.length > 0 ? surveyStats[0] : {
        totalSurveys: 0,
        agencies: [],
        brands: [],
        regions: [],
        assessmentTypes: [],
        agencyTypes: []
      };

      return NextResponse.json({
        isSeedWave: true,
        totalResponses: 0,
        totalSurveys: stats.totalSurveys,
        completionRate: 0,
        averageScore: 0,
        averageNPS: 0,
        agencyCount: stats.agencies.length,
        brandCount: stats.brands.length,
        regionCount: stats.regions.length,
        assessmentTypeCount: stats.assessmentTypes.length,
        agencies: stats.agencies.sort(),
        brands: stats.brands.sort(),
        regions: stats.regions.sort(),
        assessmentTypes: stats.assessmentTypes.sort(),
        agencyTypes: stats.agencyTypes.sort(),
        agencyDetails: agencyDetails.map(agency => ({
          agencyName: agency.agencyName,
          responseCount: 0, // No responses for seed data
          averageScore: 0,
          averageNPS: 0,
          brandCount: agency.brandCount,
          regionCount: agency.regionCount,
          surveyCount: agency.surveyCount,
          brands: agency.brands,
          regions: agency.regions,
          agencyTypes: agency.agencyTypes
        }))
      });
    }

    // Original logic for non-seed waves (responses)
    // Build aggregation pipeline for responses in this wave
    const responsePipeline = [
      // Join with surveys to get agency/brand info
      {
        $lookup: {
          from: 'surveys',
          localField: 'surveyId',
          foreignField: '_id',
          as: 'survey'
        }
      },
      { $unwind: '$survey' },
      // Join with waves to get wave info
      {
        $lookup: {
          from: 'waves',
          localField: 'survey.waveId',
          foreignField: '_id',
          as: 'wave'
        }
      },
      { $unwind: '$wave' },
      // Filter by this specific wave
      {
        $match: {
          'survey.waveId': { $eq: waveObjectId }
        }
      }
    ];

    // Get response statistics
    const responseStats = await ResponseModel.aggregate([
      ...responsePipeline,
      {
        $group: {
          _id: null,
          totalResponses: { $sum: 1 },
          averageScore: {
            $avg: {
              $avg: ['$q1Score', '$q2Score', '$q3Score', '$q4Score', '$q5Score']
            }
          },
          averageNPS: { $avg: '$npsScore' },
          agencies: { $addToSet: '$survey.agencyName' },
          brands: { $addToSet: '$survey.brand' },
          regions: { $addToSet: '$survey.country' },
          assessmentTypes: { $addToSet: '$survey.assessmentType' }
        }
      }
    ]);

    // Get agency-level details
    const agencyDetails = await ResponseModel.aggregate([
      ...responsePipeline,
      {
        $group: {
          _id: '$survey.agencyName',
          responseCount: { $sum: 1 },
          averageScore: {
            $avg: {
              $avg: ['$q1Score', '$q2Score', '$q3Score', '$q4Score', '$q5Score']
            }
          },
          averageNPS: { $avg: '$npsScore' },
          brands: { $addToSet: '$survey.brand' },
          regions: { $addToSet: '$survey.country' }
        }
      },
      {
        $project: {
          agencyName: '$_id',
          responseCount: 1,
          averageScore: { $round: ['$averageScore', 2] },
          averageNPS: { $round: ['$averageNPS', 2] },
          brandCount: { $size: '$brands' },
          regionCount: { $size: '$regions' },
          brands: 1,
          regions: 1
        }
      },
      { $sort: { averageScore: -1 } }
    ]);

    // Get total surveys in this wave (for completion rate)
    const totalSurveys = await SurveyModel.countDocuments({
      waveId: waveObjectId
    });

    const stats = responseStats.length > 0 ? responseStats[0] : {
      totalResponses: 0,
      averageScore: 0,
      averageNPS: 0,
      agencies: [],
      brands: [],
      regions: [],
      assessmentTypes: []
    };

    const completionRate = totalSurveys > 0 ? (stats.totalResponses / totalSurveys) * 100 : 0;

    return NextResponse.json({
      isSeedWave: false,
      totalResponses: stats.totalResponses,
      totalSurveys,
      completionRate: Math.round(completionRate * 100) / 100,
      averageScore: Math.round((stats.averageScore || 0) * 100) / 100,
      averageNPS: Math.round((stats.averageNPS || 0) * 100) / 100,
      agencyCount: stats.agencies.length,
      brandCount: stats.brands.length,
      regionCount: stats.regions.length,
      assessmentTypeCount: stats.assessmentTypes.length,
      agencies: stats.agencies.sort(),
      brands: stats.brands.sort(),
      regions: stats.regions.sort(),
      assessmentTypes: stats.assessmentTypes.sort(),
      agencyDetails: agencyDetails
    });

  } catch (error) {
    console.error('Error fetching wave stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch wave statistics' },
      { status: 500 }
    );
  }
} 