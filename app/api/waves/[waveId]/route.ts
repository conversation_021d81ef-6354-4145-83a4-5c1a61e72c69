import { NextRequest, NextResponse } from 'next/server';
import { Types } from 'mongoose';
import dbConnect from '../../../../lib/mongodb';
import { WaveModel } from '../../../../lib/models/wave';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ waveId: string }> }
) {
  try {
    await dbConnect();

    const { waveId } = await params;
    
    if (!Types.ObjectId.isValid(waveId)) {
      return NextResponse.json(
        { error: 'Invalid wave ID' },
        { status: 400 }
      );
    }

    const wave = await WaveModel.findById(waveId);

    if (!wave) {
      return NextResponse.json(
        { error: 'Wave not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      id: wave._id.toString(),
      name: wave.name,
      status: wave.status,
      config: wave.config,
      createdAt: wave.createdAt,
      updatedAt: wave.updatedAt,
    });
  } catch (error) {
    console.error('Error fetching wave:', error);
    return NextResponse.json(
      { error: 'Failed to fetch wave' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ waveId: string }> }
) {
  try {
    await dbConnect();

    const { waveId } = await params;
    
    if (!Types.ObjectId.isValid(waveId)) {
      return NextResponse.json(
        { error: 'Invalid wave ID' },
        { status: 400 }
      );
    }

    const wave = await WaveModel.findById(waveId);

    if (!wave) {
      return NextResponse.json(
        { error: 'Wave not found' },
        { status: 404 }
      );
    }

    // Only allow deletion of draft waves
    if (wave.status !== 'draft') {
      return NextResponse.json(
        { error: 'Only draft waves can be deleted' },
        { status: 400 }
      );
    }

    await WaveModel.findByIdAndDelete(waveId);

    return NextResponse.json({
      success: true,
      message: 'Draft wave deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting wave:', error);
    return NextResponse.json(
      { error: 'Failed to delete wave' },
      { status: 500 }
    );
  }
} 