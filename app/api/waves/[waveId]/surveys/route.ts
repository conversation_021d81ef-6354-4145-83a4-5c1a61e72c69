import { NextRequest, NextResponse } from 'next/server';
import { Types } from 'mongoose';
import dbConnect from '../../../../../lib/mongodb';
import { SurveyModel } from '../../../../../lib/models/survey';
import { fromSurveyDocToVm } from '../../../../../lib/api/surveys';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ waveId: string }> }
) {
  try {
    await dbConnect();

    const { waveId } = await params;
    const waveObjectId = Types.ObjectId.createFromHexString(waveId);

    // Get all surveys for this wave
    const surveys = await SurveyModel.find({ waveId: waveObjectId });
    const surveyVms = await Promise.all(surveys.map(fromSurveyDocToVm));

    return NextResponse.json(surveyVms);

  } catch (error) {
    console.error('Error fetching wave surveys:', error);
    return NextResponse.json(
      { error: 'Failed to fetch wave surveys' },
      { status: 500 }
    );
  }
} 