'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import {
  Box,
  Typography,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Paper,
  Container,
  Divider,
  Card,
  CardContent,
  CardActions,
  Chip,
} from '@mui/material';
import { Login as LoginIcon, FileCopy as CopyIcon } from '@mui/icons-material';

interface TestDataItem {
  email: string;
  passcode: string;
  waveId: string;
  surveyCount: number;
  debugUrl: string;
}

interface TestDataResponse {
  success: boolean;
  testData: TestDataItem[];
  error?: string;
}

interface LoginResponse {
  success: boolean;
  user: {
    userName: string;
    userEmail: string;
    waveId: string;
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  surveys: any[];
  passcode: string;
  error?: string;
}

export default function LoginClient() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [passcode, setPasscode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isAutoLoggingIn, setIsAutoLoggingIn] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [testData, setTestData] = useState<TestDataItem[]>([]);
  const [loadingTestData, setLoadingTestData] = useState(true);

  // Load test data on component mount
  useEffect(() => {
    const fetchTestData = async () => {
      try {
        const response = await fetch('/api/auth/test-data');
        const data: TestDataResponse = await response.json();

        if (data.success) {
          setTestData(data.testData);
        }
      } catch (error) {
        console.error('Error loading test data:', error);
      } finally {
        setLoadingTestData(false);
      }
    };

    fetchTestData();
  }, []);

  const performLogin = useCallback(
    async (emailValue: string, passcodeValue: string) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: emailValue,
            passcode: passcodeValue,
          }),
        });

        const data: LoginResponse = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Login failed');
        }

        // Store auth info in session storage
        sessionStorage.setItem(
          'authUser',
          JSON.stringify({
            userName: data.user.userName,
            userEmail: data.user.userEmail,
            waveId: data.user.waveId,
            passcode: data.passcode,
            surveys: data.surveys,
          })
        );

        // Redirect to survey dashboard
        router.push('/dashboard');
      } catch (error) {
        console.error('Login error:', error);
        setError(error instanceof Error ? error.message : 'Login failed');
      } finally {
        setIsLoading(false);
        setIsAutoLoggingIn(false);
      }
    },
    [router]
  );

  // Pre-fill form from query params and auto-login if both are provided
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      const emailParam = params.get('email') || '';
      const passcodeParam = params.get('passcode') || '';

      setEmail(emailParam);
      setPasscode(passcodeParam);

      // Auto-login if both email and passcode are provided
      if (emailParam && passcodeParam) {
        setIsAutoLoggingIn(true);
        performLogin(emailParam, passcodeParam);
      }
    }
  }, [performLogin]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      setError('Please enter your email address');
      return;
    }

    if (!passcode.trim()) {
      setError('Please enter your passcode');
      return;
    }

    await performLogin(email.trim(), passcode.trim());
  };

  const handleQuickLogin = async (testItem: TestDataItem) => {
    await performLogin(testItem.email, testItem.passcode);
  };

  const copyToForm = (testItem: TestDataItem) => {
    setEmail(testItem.email);
    setPasscode(testItem.passcode);
    setError(null);
  };

  return (
    <Container maxWidth="md">
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          py: 4,
        }}
      >
        <Box sx={{ width: '100%' }}>
          {/* Auto-login loading state */}
          {isAutoLoggingIn && (
            <Paper
              sx={{ p: 4, maxWidth: 400, mx: 'auto', textAlign: 'center' }}
            >
              <CircularProgress size={48} sx={{ mb: 3 }} />
              <Typography variant="h5" gutterBottom>
                Signing you in...
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Please wait while we authenticate your credentials
              </Typography>
            </Paper>
          )}

          {/* Main Login Form - only show if not auto-logging in */}
          {!isAutoLoggingIn && (
            <Paper sx={{ p: 4, mb: 4, maxWidth: 400, mx: 'auto' }}>
              <Box sx={{ textAlign: 'center', mb: 4 }}>
                <Typography variant="h4" fontWeight={700} gutterBottom>
                  Survey Login
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Enter your email address and 6-digit passcode to access your
                  surveys
                </Typography>
              </Box>

              <form onSubmit={handleSubmit}>
                <TextField
                  fullWidth
                  label="Email Address"
                  type="email"
                  variant="outlined"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  sx={{ mb: 2 }}
                  disabled={isLoading}
                />

                <TextField
                  fullWidth
                  label="Passcode"
                  variant="outlined"
                  value={passcode}
                  onChange={(e) => setPasscode(e.target.value)}
                  placeholder="Enter 6-digit passcode"
                  inputProps={{
                    maxLength: 6,
                    style: {
                      textAlign: 'center',
                      fontSize: '1.5rem',
                      letterSpacing: '0.5rem',
                    },
                  }}
                  sx={{ mb: 3 }}
                  disabled={isLoading}
                />

                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={isLoading || !email.trim() || !passcode.trim()}
                  startIcon={isLoading ? <CircularProgress size={20} /> : null}
                  sx={{ mb: 2 }}
                >
                  {isLoading ? 'Signing In...' : 'Sign In'}
                </Button>
              </form>

              <Box sx={{ textAlign: 'center', mt: 3 }}>
                <Typography variant="body2" color="text.secondary">
                  Don&apos;t have a passcode? Check your email for survey
                  invitation.
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ mt: 1 }}
                >
                  Use the email address where you received the passcode.
                </Typography>
              </Box>
            </Paper>
          )}

          {/* Test Data Section */}
          {!loadingTestData && testData.length > 0 && (
            <Paper sx={{ p: 4 }}>
              <Box sx={{ textAlign: 'center', mb: 3 }}>
                <Typography variant="h5" fontWeight={600} gutterBottom>
                  Test Data
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Use these test accounts for development and testing
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {testData.map((item, index) => (
                  <Card key={index}>
                    <CardContent>
                      <Box sx={{ mb: 2 }}>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          gutterBottom
                        >
                          Email:
                        </Typography>
                        <Typography
                          variant="body2"
                          fontFamily="monospace"
                          sx={{ wordBreak: 'break-all' }}
                        >
                          {item.email}
                        </Typography>
                      </Box>

                      <Box sx={{ mb: 2 }}>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          gutterBottom
                        >
                          Passcode:
                        </Typography>
                        <Chip
                          label={item.passcode}
                          variant="outlined"
                          sx={{ fontFamily: 'monospace', fontSize: '1.1rem' }}
                        />
                      </Box>

                      <Box sx={{ mb: 2 }}>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          gutterBottom
                        >
                          Wave ID:
                        </Typography>
                        <Typography
                          variant="body2"
                          fontFamily="monospace"
                          fontSize="0.8rem"
                        >
                          {item.waveId}
                        </Typography>
                      </Box>

                      <Box sx={{ mb: 2 }}>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          gutterBottom
                        >
                          Surveys:
                        </Typography>
                        <Chip
                          label={`${item.surveyCount} surveys`}
                          variant="outlined"
                          color={item.surveyCount > 0 ? 'success' : 'error'}
                          size="small"
                        />
                        {item.surveyCount === 0 && (
                          <Typography
                            variant="caption"
                            color="error"
                            sx={{ display: 'block', mt: 1 }}
                          >
                            ⚠️ No surveys found - this will cause login to fail
                          </Typography>
                        )}
                      </Box>

                      <Box sx={{ mb: 2 }}>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          gutterBottom
                        >
                          Sample Login URL:
                        </Typography>
                        <Typography
                          variant="body2"
                          fontFamily="monospace"
                          sx={{ wordBreak: 'break-all', mb: 1 }}
                        >
                          {`${
                            typeof window !== 'undefined'
                              ? window.location.origin
                              : ''
                          }/login?email=${encodeURIComponent(
                            item.email
                          )}&passcode=${encodeURIComponent(item.passcode)}`}
                        </Typography>
                        {typeof window !== 'undefined' && (
                          <Button
                            variant="text"
                            size="small"
                            href={`${
                              window.location.origin
                            }/login?email=${encodeURIComponent(
                              item.email
                            )}&passcode=${encodeURIComponent(item.passcode)}`}
                            target="_blank"
                            sx={{
                              textTransform: 'none',
                              p: 0,
                              minWidth: 'auto',
                            }}
                          >
                            Open Login URL
                          </Button>
                        )}
                      </Box>
                    </CardContent>

                    <CardActions
                      sx={{
                        pt: 0,
                        flexDirection: 'column',
                        alignItems: 'stretch',
                      }}
                    >
                      <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                        <Button
                          size="small"
                          startIcon={<LoginIcon />}
                          onClick={() => handleQuickLogin(item)}
                          disabled={isLoading || item.surveyCount === 0}
                          variant="contained"
                          sx={{ mr: 1 }}
                        >
                          Quick Login
                        </Button>
                        <Button
                          size="small"
                          startIcon={<CopyIcon />}
                          onClick={() => copyToForm(item)}
                          disabled={isLoading}
                          variant="outlined"
                        >
                          Copy to Form
                        </Button>
                      </Box>
                      <Button
                        size="small"
                        href={item.debugUrl}
                        target="_blank"
                        variant="text"
                        sx={{ textTransform: 'none' }}
                      >
                        🔍 Debug Info
                      </Button>
                    </CardActions>
                  </Card>
                ))}
              </Box>

              {testData.length > 0 && (
                <Box sx={{ mt: 3, textAlign: 'center' }}>
                  <Divider sx={{ mb: 2 }} />
                  <Typography variant="body2" color="text.secondary">
                    💡 Quick login: Use the &ldquo;Quick Login&rdquo; button
                    above to test with any of the accounts below.
                  </Typography>
                </Box>
              )}
            </Paper>
          )}

          {loadingTestData && (
            <Paper sx={{ p: 4, textAlign: 'center' }}>
              <CircularProgress />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                Loading test data...
              </Typography>
            </Paper>
          )}
        </Box>
      </Box>
    </Container>
  );
}
