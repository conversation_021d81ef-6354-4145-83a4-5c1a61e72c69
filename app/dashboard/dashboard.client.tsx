'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Container,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Paper,
  CssBaseline,
  IconButton,
  ListItemButton,
  CircularProgress,
  useTheme,
} from '@mui/material';
import DashboardIcon from '@mui/icons-material/Dashboard';
import LogoutIcon from '@mui/icons-material/Logout';
import MenuIcon from '@mui/icons-material/Menu';
import DarkModeToggle from '../_components/dark-mode-toggle';
import LanguageSelector from '../_components/language-selector';
import CustomThemeProvider from '../_components/theme-provider';
import { useTheme as useCustomTheme } from '../_components/theme-provider';
import { useTranslation } from '../_components/translation-provider';
import { ElevatedFormControl } from '../_components/ElevatedFormControl';

interface AuthUser {
  userName: string;
  userEmail: string;
  waveId: string;
  passcode: string;
  surveys: Survey[];
}

interface SurveyProgress {
  completedQuestions: number;
  totalQuestions: number;
  isComplete: boolean;
  percentComplete: number;
}

interface Survey {
  id: string;
  accountName: string;
  agencyName: string;
  agencyType: string;
  brand: string;
  country: string;
  region: string;
  assessmentType: string;
  userName: string;
  userEmail: string;
  userStatus: string;
  inScope: string;
  notes: string;
  waveId: string;
}

const drawerWidth = 240;

function DashboardContent() {
  const router = useRouter();
  const theme = useTheme();
  const [authUser, setAuthUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [mobileOpen, setMobileOpen] = useState(false);
  const customTheme = useCustomTheme();
  const { t } = useTranslation();
  const [surveyProgress, setSurveyProgress] = useState<{
    [surveyId: string]: SurveyProgress;
  }>({});
  const [completedSurvey, setCompletedSurvey] = useState<string | null>(null);

  useEffect(() => {
    // Check for authenticated user in session storage
    const storedUser = sessionStorage.getItem('authUser');

    if (storedUser) {
      try {
        const user: AuthUser = JSON.parse(storedUser);
        setAuthUser(user);
      } catch (error) {
        console.error('Error parsing stored user:', error);
        // Clear invalid data and redirect to login
        sessionStorage.removeItem('authUser');
        router.push('/login');
      }
    } else {
      // No authentication found, redirect to login
      router.push('/login');
    }

    // Check for completion status in URL
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      const completed = params.get('completed');
      if (completed) {
        setCompletedSurvey(decodeURIComponent(completed));
        // Remove the parameter from URL without page reload
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete('completed');
        window.history.replaceState({}, '', newUrl.toString());
      }
    }

    setIsLoading(false);
  }, [router]);

  // Load survey progress after user is loaded
  useEffect(() => {
    const loadSurveyProgress = async () => {
      if (!authUser || authUser.surveys.length === 0) {
        return;
      }

      try {
        const surveyIds = authUser.surveys.map((survey) => survey.id);
        const response = await fetch('/api/responses/survey-progress', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            surveyIds,
            userEmail: authUser.userEmail,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          setSurveyProgress(data.progressMap || {});
        }
      } catch (error) {
        console.error('Error loading survey progress:', error);
      }
    };

    loadSurveyProgress();
  }, [authUser]);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleLogout = () => {
    sessionStorage.removeItem('authUser');
    router.push('/login');
  };

  const handleTakeSurvey = (surveyId: string) => {
    router.push(`/survey/${surveyId}`);
  };

  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (!authUser) {
    return null; // Will redirect to login
  }

  const drawer = (
    <>
      <Toolbar sx={{ minHeight: 64 }}>
        <Typography variant="h6">ECHO360</Typography>
      </Toolbar>
      <Divider />
      <List>
        <ListItem disablePadding>
          <ListItemButton selected>
            <ListItemIcon>
              <DashboardIcon color="primary" />
            </ListItemIcon>
            <ListItemText primary={t('dashboard.title')} />
          </ListItemButton>
        </ListItem>
      </List>
      <Box sx={{ flexGrow: 1 }} />
      <Divider />
      <List sx={{ mb: 1 }}>
        <ListItem disablePadding>
          <ListItemButton onClick={customTheme.toggleDarkMode}>
            <ListItemIcon>
              <DarkModeToggle />
            </ListItemIcon>
            <ListItemText primary={t('dashboard.darkMode')} />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding>
          <LanguageSelector />
        </ListItem>
      </List>
      <Divider />
      <List sx={{ mb: 2 }}>
        <ListItem disablePadding>
          <ListItemButton onClick={handleLogout}>
            <ListItemIcon>
              <LogoutIcon color="action" />
            </ListItemIcon>
            <ListItemText primary={t('dashboard.logout')} />
          </ListItemButton>
        </ListItem>
      </List>
    </>
  );

  return (
    <Box
      sx={{
        display: 'flex',
        bgcolor: theme.palette.background.default,
        minHeight: '100vh',
      }}
    >
      <CssBaseline />

      {/* Mobile Drawer */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile.
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
            bgcolor: 'background.paper',
          },
        }}
      >
        {drawer}
      </Drawer>

      {/* Desktop Drawer */}
      <Drawer
        variant="permanent"
        sx={{
          display: { xs: 'none', md: 'block' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
            bgcolor: 'background.paper',
            borderRight: 0,
          },
        }}
        open
      >
        {drawer}
      </Drawer>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 0,
          width: { xs: '100%', md: `calc(100% - ${drawerWidth}px)` },
          ml: { xs: 0, md: `${drawerWidth}px` },
          minHeight: '100vh',
          bgcolor: theme.palette.background.default,
        }}
      >
        {/* Top Bar */}
        <AppBar
          position="fixed"
          color="inherit"
          elevation={0}
          sx={{
            width: { xs: '100%', md: `calc(100% - ${drawerWidth}px)` },
            ml: { xs: 0, md: `${drawerWidth}px` },
            borderBottom: 1,
            borderColor: 'divider',
            bgcolor: 'background.paper',
          }}
        >
          <Toolbar sx={{ justifyContent: 'space-between', minHeight: 64 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="start"
                onClick={handleDrawerToggle}
                sx={{ mr: 2, display: { md: 'none' } }}
              >
                <MenuIcon />
              </IconButton>
              <Typography
                variant="h6"
                sx={{ display: { xs: 'none', sm: 'block' } }}
              >
                ECHO360
              </Typography>
            </Box>
            <Typography variant="body1" color="text.secondary">
              {t('dashboard.welcome')} {authUser.userName}
            </Typography>
          </Toolbar>
        </AppBar>
        <Toolbar sx={{ minHeight: 64 }} /> {/* Spacer for AppBar */}
        {/* Completion Banner */}
        {completedSurvey && (
          <Container maxWidth="lg" sx={{ mt: 2, px: { xs: 2, sm: 3 } }}>
            <Alert
              severity="success"
              sx={{
                mb: 2,
                '& .MuiAlert-message': {
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                },
              }}
              onClose={() => setCompletedSurvey(null)}
            >
              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                {t('survey.completionMessage', { agency: completedSurvey })}
              </Typography>
            </Alert>
          </Container>
        )}
        <Container maxWidth="lg" sx={{ mt: 2, mb: 4, px: { xs: 2, sm: 3 } }}>
          <Typography
            variant="h4"
            gutterBottom
            sx={{
              fontWeight: 600,
              fontSize: { xs: '1.75rem', sm: '2.125rem' },
            }}
          >
            {t('dashboard.title')}
          </Typography>

          <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
            {t('dashboard.surveysCount', {
              count: authUser.surveys.length,
              plural: authUser.surveys.length !== 1 ? 's' : '',
            })}
          </Typography>

          {authUser.surveys.length === 0 ? (
            <Alert severity="info">{t('dashboard.noSurveys')}</Alert>
          ) : (
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: {
                  xs: '1fr',
                  sm: '1fr',
                  md: '1fr 1fr',
                  lg: '1fr 1fr 1fr',
                },
                gap: 6,
                justifyContent: 'center',
              }}
            >
              {authUser.surveys.map((survey) => {
                const progress = surveyProgress[survey.id];
                const isComplete = progress?.isComplete || false;
                const percentComplete = progress?.percentComplete || 0;
                const completedQuestions = progress?.completedQuestions || 0;
                const totalQuestions = progress?.totalQuestions || 5;

                return (
                  <Paper
                    key={survey.id}
                    elevation={0}
                    sx={{
                      borderRadius: 2,
                      border: 1,
                      borderColor: 'divider',
                      p: { xs: 2, sm: 3 },
                      display: 'flex',
                      flexDirection: 'column',
                      width: '100%',
                      position: 'relative',
                      overflow: 'hidden',
                    }}
                  >
                    {/* Status Ribbon */}
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 18,
                        right: -60,
                        width: 180,
                        height: 28,
                        color: isComplete
                          ? 'dark.main'
                          : completedQuestions > 0
                          ? 'success.main'
                          : 'warning.main',
                        outlineColor: isComplete
                          ? 'dark.main'
                          : completedQuestions > 0
                          ? 'success.main'
                          : 'warning.main',
                        outlineWidth: 1,
                        outlineStyle: 'solid',
                        fontWeight: 700,
                        fontSize: '0.65rem',
                        textTransform: 'uppercase',
                        letterSpacing: '1px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        transform: 'rotate(45deg)',
                        zIndex: 2,
                        // boxShadow: 2,
                        pointerEvents: 'none',
                        userSelect: 'none',
                      }}
                    >
                      {isComplete
                        ? t('dashboard.completed')
                        : completedQuestions > 0
                        ? t('dashboard.inProgress')
                        : t('dashboard.pending')}
                    </Box>

                    <Typography
                      variant="h6"
                      gutterBottom
                      sx={{
                        fontSize: { xs: '1.1rem', sm: '1.25rem' },
                        lineHeight: 1.3,
                        pr: 6, // Add padding to prevent text overlap with ribbon
                      }}
                    >
                      {survey.assessmentType ===
                      'Agency-on-Anheuser Busch In-Bev'
                        ? t('dashboard.agencyAssessmentOfABI')
                        : t('dashboard.abiAssessmentOfAgency')}
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      gutterBottom
                      sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}
                    >
                      {survey.agencyName} • {survey.brand}
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mb: 2, fontSize: { xs: '0.875rem', sm: '1rem' } }}
                    >
                      {survey.country} • {survey.agencyType}
                    </Typography>

                    {/* Progress Bar */}
                    {completedQuestions > 0 && (
                      <Box sx={{ mb: 2 }}>
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            mb: 1,
                          }}
                        >
                          <Typography variant="body2" color="text.secondary">
                            Progress
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {completedQuestions}/{totalQuestions} questions (
                            {percentComplete}%)
                          </Typography>
                        </Box>
                        <Box
                          sx={{
                            width: '100%',
                            bgcolor: 'grey.200',
                            borderRadius: 1,
                            height: 6,
                          }}
                        >
                          <Box
                            sx={{
                              width: `${percentComplete}%`,
                              bgcolor: isComplete
                                ? 'success.main'
                                : 'primary.main',
                              height: '100%',
                              borderRadius: 1,
                              transition: 'width 0.3s ease',
                            }}
                          />
                        </Box>
                      </Box>
                    )}

                    <ElevatedFormControl>
                      <Button
                        size="small"
                        variant="contained"
                        onClick={() => handleTakeSurvey(survey.id)}
                        disabled={isComplete}
                        fullWidth
                        sx={{
                          fontSize: { xs: '0.875rem', sm: '1rem' },
                        }}
                      >
                        {isComplete
                          ? t('dashboard.completed')
                          : completedQuestions > 0
                          ? t('dashboard.continueSurvey')
                          : t('dashboard.startSurvey')}
                      </Button>
                    </ElevatedFormControl>
                  </Paper>
                );
              })}
            </Box>
          )}

          <Paper
            sx={{
              mt: 4,
              p: { xs: 2, sm: 3 },
              bgcolor: 'background.paper',
              borderRadius: 2,
            }}
            elevation={0}
          >
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
                wordBreak: 'break-word',
              }}
            >
              <strong>{t('dashboard.yourPasscode')}</strong> {authUser.passcode}{' '}
              |<strong> {t('dashboard.email')}</strong> {authUser.userEmail}
            </Typography>
          </Paper>
        </Container>
      </Box>
    </Box>
  );
}

export default function DashboardClient() {
  return (
    <CustomThemeProvider>
      <DashboardContent />
    </CustomThemeProvider>
  );
}
