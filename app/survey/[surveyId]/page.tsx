import SurveyClient from './survey.client';
import { getSurveyVm } from '../../../lib/api/surveys';

export default async function Survey({
  params,
}: {
  params: Promise<{ surveyId: string }>;
}) {
  const { surveyId } = await params;

  if (!surveyId) {
    throw new Error('Invalid Survey ID');
  }

  const surveyVm = await getSurveyVm(surveyId as string);

  if (!surveyVm) {
    throw new Error('Invalid Survey');
  }

  return <SurveyClient surveyVm={surveyVm} />;
}
