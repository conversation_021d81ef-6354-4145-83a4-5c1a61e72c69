# Echo360 Cursor/GitHub Copilot Configuration

## COMMIT MESSAGE FORMAT

Format commit messages as:

```
[scope]: Brief description of changes

- Detailed bullet points about what changed
- Another detail point if needed

Relates to: #migration-number (if applicable)
```

Where `scope` should be one of:

- `feature`: For feature updates
- `deps`: For dependency updates
- `docs`: For documentation updates
- `infra`: For infrastructure or build changes

## README instructions

When suggesting README updates:

- Keep the main project description concise and clear
- Include clear installation instructions
- Update environment configuration documentation
- Include troubleshooting section for common issues

The README should cover:

1. Project overview and purpose
2. Installation and setup instructions
3. Usage examples for both backend and frontend
4. API documentation
5. Contributing guidelines

## CHANGELOG

When updating the changelog:

- Group changes under semantic versioning headers (e.g., ## 1.2.0)
- Use the following categories:
  - **Added**: For new features
  - **Changed**: For changes in existing functionality
  - **Deprecated**: For soon-to-be removed features
  - **Removed**: For removed features
  - **Fixed**: For bug fixes
  - **Security**: For security updates
- Include the date of the version release
- Reference relevant issue or PR numbers
- Highlight breaking changes prominently

## Preferred coding style

style:

- Use typescript
- Use react
- Use next.js
- Use material-ui
- Use spaces as tabs, default 2 spaces
- Use semicolons
- Use single quotes for strings
- Use arrow functions instead of function declarations
- Use const for variables that are not reassigned
- Use let for variables that are reassigned
- Avoid non-standard code/patterns when using typescript, react, next.js, and material-ui. Best to use standard practices for these.

# Files to ignore

ignore:

- ".env"
