# Echo360 Usage Guide

## Overview

Echo360 has two main import systems for setting up survey campaigns:

1. **Import Surveys** - Creates waves with survey assignments
2. **Import Responses** - Imports completed survey responses (creates missing waves/surveys automatically)

## 🚨 Email Safety

**Good news!** Email sending is currently disabled in the code. The `launchWave()` and `emailBlastLatestWave()` functions have their email calls commented out, so you can safely test without sending emails.

## 1. Import Surveys (Creating a Wave)

### Purpose

Creates a new "wave" (survey campaign) with all survey assignments from a CSV file.

### Access

Navigate to: `/admin/import-surveys`

### CSV Format Required

Your CSV must have these exact headers:

- Account Name
- Agency Name
- Agency Type
- Brand
- Country
- Region
- Assessment Type
- User Name
- User Email
- User Status
- In Scope?
- NOTES

### Example CSV Row

```csv
Account Name,Agency Name,Agency Type,Brand,Country,Region,Assessment Type,User Name,User Email,User Status,In Scope?,NOTES
"Anheuser Busch In-Bev","Creative Agency ABC","Creative","Budweiser","USA","North America","Anheuser Busch In-Bev-on-Agency","<PERSON> Smith","<EMAIL>","Active","Yes","Q1 2024 review"
```

### What Happens When You Import

1. **Creates a Wave** - New survey campaign with the name you specify
2. **Creates Surveys** - One survey record per CSV row
3. **Generates Passcodes** - Unique passcode per user email (for authentication)
4. **Wave Status** - Starts as "draft" (safe for testing)

### Steps to Import Surveys

1. Go to `/admin/import-surveys`
2. Drag/drop or click to select your CSV file
3. Enter a wave name (e.g., "Q1 2024 Agency Review")
4. Click "Import Wave"
5. System validates headers and data
6. Creates wave + surveys + passcodes atomically

## 2. Import Responses (Loading Completed Surveys)

### Purpose

Imports completed survey responses from a CSV file. **Automatically creates missing waves and surveys** - perfect for historical data migration!

### Access

Navigate to: `/admin/import-responses`

### 🆕 Automatic Creation Feature

- **Missing Waves**: Creates waves using the "Evaluation Name" column
- **Missing Surveys**: Creates surveys based on response data (agency, brand, country, assessment type)
- **Wave Status**: Auto-created waves are marked as "completed" (historical data)
- **Placeholder Emails**: Generates placeholder emails for auto-created surveys

### CSV Format Required

Your CSV must have these exact headers:

- Agency
- Type
- Zone
- Country
- Brand
- Brand Category
- Reporting Month
- Account
- Assessor Name
- Assessor Type
- Section
- Score
- Comment
- Evaluation Name

### Important: Response Grouping

- **One CSV row = One question response**
- System groups multiple rows by assessor to create complete survey responses
- Each assessor should have 5 rows (one per section/question)

### Example CSV Rows (for one complete response)

```csv
Agency,Type,Zone,Country,Brand,Brand Category,Reporting Month,Account,Assessor Name,Assessor Type,Section,Score,Comment,Evaluation Name
"Creative Agency ABC","Creative","North America","USA","Budweiser","Beer","2024-01","Anheuser Busch In-Bev","John Smith","Agency","1 People and Partnership",4,"Great collaboration","Q1 2024"
"Creative Agency ABC","Creative","North America","USA","Budweiser","Beer","2024-01","Anheuser Busch In-Bev","John Smith","Agency","2 Account Management and Process",3,"Could improve","Q1 2024"
"Creative Agency ABC","Creative","North America","USA","Budweiser","Beer","2024-01","Anheuser Busch In-Bev","John Smith","Agency","3 Strategy",5,"Excellent work","Q1 2024"
"Creative Agency ABC","Creative","North America","USA","Budweiser","Beer","2024-01","Anheuser Busch In-Bev","John Smith","Agency","4 Creativity",4,"Very creative","Q1 2024"
"Creative Agency ABC","Creative","North America","USA","Budweiser","Beer","2024-01","Anheuser Busch In-Bev","John Smith","Agency","5 Media Planning and Execution",3,"Room for improvement","Q1 2024"
```

### What Happens When You Import Responses

1. **Creates Missing Waves** - Uses "Evaluation Name" as wave name (status: "completed")
2. **Creates Missing Surveys** - Derives survey details from response data
3. **Groups by Assessor** - Combines multiple rows into single response records
4. **Calculates Scores** - Auto-calculates NPS and overall rating
5. **Updates Survey Status** - Marks matched surveys as "Submitted"

### Steps to Import Responses

1. Go to `/admin/import-responses`
2. Drag/drop or click to select your CSV file
3. System validates headers and groups data
4. Click "Import Responses"
5. System creates missing waves/surveys and imports response records
6. **Results show**: responses imported + waves created + surveys created

## 3. Typical Workflow

### For New Survey Campaign

1. **Prepare Survey CSV** with all survey assignments
2. **Import Surveys** to create the wave
3. **Review in Admin** - Check wave was created correctly
4. **When ready to launch** - Use launch functionality (emails currently disabled)

### For Historical Data Migration (NEW!)

1. **Just import responses** - No need to create surveys first!
2. **System auto-creates** waves and surveys from response data
3. **Review analytics** to verify data imported correctly

### For Mixed Scenarios

1. **Import surveys** for future campaigns
2. **Import responses** for historical data (auto-creates missing pieces)
3. **System handles** both scenarios seamlessly

## 4. Data Validation

### Survey Import Validation

- All required headers must be present
- No empty required fields (Agency Name, User Email, etc.)
- Duplicate wave names not allowed

### Response Import Validation

- All required headers must be present
- Must have Agency, Assessor Name, Section, and Score
- **No longer requires existing surveys** - creates them automatically!
- Duplicate responses for same survey not allowed

## 5. Error Handling

Both import systems provide detailed error reporting:

- **Success count** - How many records imported
- **Creation count** - How many waves/surveys created automatically (responses only)
- **Error list** - Specific issues with individual records
- **Validation errors** - Header or format problems

## 6. Admin Navigation

- `/admin` - Main dashboard
- `/admin/waves` - View all waves (including auto-created ones)
- `/admin/import-surveys` - Import new survey campaigns
- `/admin/import-responses` - Import response data (with auto-creation)
- `/admin/dashboard` - Analytics and reporting

## 7. Safety Features

- **No emails sent** during testing (commented out in code)
- **Atomic operations** - If survey import fails, wave is rolled back
- **Duplicate prevention** - Won't create duplicate waves or responses
- **Validation** - Comprehensive data validation before import
- **Auto-creation** - Handles missing waves/surveys gracefully

## Next Steps

1. **Test with small CSV files** first
2. **Try response import** without creating surveys first (test auto-creation)
3. **Review imported data** in admin interface
4. **When ready for production** - Uncomment email functionality in `lib/api/waves.ts`
5. **Configure email templates** and SMTP settings as needed

## Troubleshooting

### Common Issues

- **Header mismatch** - Ensure CSV headers match exactly (case-sensitive)
- **Empty rows** - Remove any blank rows from CSV
- **Encoding** - Save CSV as UTF-8 if special characters appear garbled

### New Auto-Creation Features

- **Placeholder emails** - Auto-created surveys use generated placeholder emails
- **Wave naming** - Uses "Evaluation Name" column for wave names
- **Status handling** - Auto-created waves marked as "completed"

### Getting Help

- Check browser console for detailed error messages
- Review the error list provided by import results
- Verify CSV format matches expected headers exactly
