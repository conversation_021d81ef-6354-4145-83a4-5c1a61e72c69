import 'dotenv-flow/config';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

// Test bcrypt functionality
async function testBcrypt() {
  console.log('🧪 Testing bcrypt password hashing...');

  const password = 'TestPassword123!';
  const salt = await bcrypt.genSalt(12);
  const hashedPassword = await bcrypt.hash(password, salt);

  console.log(`Original password: ${password}`);
  console.log(`Hashed password: ${hashedPassword}`);

  const isValid = await bcrypt.compare(password, hashedPassword);
  console.log(`Password verification: ${isValid ? '✅ PASS' : '❌ FAIL'}`);

  return isValid;
}

// Test JWT functionality
function testJWT() {
  console.log('\n🧪 Testing JWT token generation...');

  const secret = 'test-jwt-secret';
  const payload = {
    id: 'test-admin-id',
    email: '<EMAIL>',
    role: 'admin',
  };

  const token = jwt.sign(payload, secret, { expiresIn: '8h' });
  console.log(`Generated JWT token: ${token.substring(0, 50)}...`);

  try {
    const decoded = jwt.verify(token, secret) as any;
    console.log(`Decoded payload:`, {
      id: decoded.id,
      email: decoded.email,
      role: decoded.role,
    });
    console.log('JWT verification: ✅ PASS');
    return true;
  } catch (error) {
    console.log('JWT verification: ❌ FAIL');
    console.error(error);
    return false;
  }
}

// Test imports
function testImports() {
  console.log('🧪 Testing module imports...');

  try {
    // These will throw if there are syntax errors
    require('../lib/models/admin');
    require('../lib/api/admin-auth');
    console.log('Admin model import: ✅ PASS');
    console.log('Admin auth library import: ✅ PASS');
    return true;
  } catch (error) {
    console.log('Module imports: ❌ FAIL');
    console.error(error);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Admin Authentication System Test Suite');
  console.log('=========================================\n');

  const results = {
    bcrypt: await testBcrypt(),
    jwt: testJWT(),
    imports: testImports(),
  };

  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${test.toUpperCase()}: ${passed ? '✅ PASS' : '❌ FAIL'}`);
  });

  const allPassed = Object.values(results).every((result) => result);
  console.log(
    `\n🎯 Overall: ${
      allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'
    }`
  );

  if (allPassed) {
    console.log('\n✨ The admin authentication system is ready to use!');
  }
}

runTests().catch(console.error);
