import fs from 'fs';
import path from 'path';

export function loadEmailTemplate(emailTemplateFilename: string): string {
  const filePath = path.join(process.cwd(), 'templates', emailTemplateFilename);
  return fs.readFileSync(filePath, 'utf8');
}

export function processHtmlTemplate(
  html: string,
  data?: Record<string, string>
) {
  // Replace placeholders deliminted by square brackets
  return html.replace(/\[([A-Za-z0-9_]+)]/g, (_, key) => data?.[key] || '');
}
