import 'dotenv-flow/config'; // Needs to be before dbConnect import
import dbConnect from '../lib/mongodb';
import { AdminModel } from '../lib/models/admin';

const INITIAL_ADMINS = [
  {
    email: '<EMAIL>',
    password: 'SuperAdmin123!',
    name: 'superadmin',
    role: 'superadmin' as const,
  },
  {
    email: '<EMAIL>',
    password: 'RegularAdmin123!',
    name: 'admin',
    role: 'admin' as const,
  },
];

async function seedAdmins() {
  try {
    await dbConnect();

    // Check if any admins already exist
    const existingAdminCount = await AdminModel.countDocuments();
    if (existingAdminCount > 0) {
      console.log(
        `Found ${existingAdminCount} existing admins. Skipping seeding.`
      );
      console.log(
        'If you want to reset admin accounts, please clear the admin collection first.'
      );
      return;
    }

    console.log('No existing admins found. Creating initial admin accounts...');

    // Create initial admins
    for (const adminData of INITIAL_ADMINS) {
      try {
        const admin = new AdminModel(adminData);
        await admin.save();
        console.log(`✅ Created ${adminData.role}: ${adminData.email}`);
      } catch (error) {
        console.error(`❌ Failed to create ${adminData.email}:`, error);
      }
    }

    console.log('\n🎉 Admin seeding completed!');
    console.log('\n📋 Admin Accounts Created:');
    console.log('='.repeat(50));

    INITIAL_ADMINS.forEach((admin) => {
      console.log(`Email: ${admin.email}`);
      console.log(`Password: ${admin.password}`);
      console.log(`Role: ${admin.role}`);
      console.log(`Name: ${admin.name}`);
      console.log('-'.repeat(30));
    });

    console.log('\n⚠️  IMPORTANT SECURITY NOTES:');
    console.log(
      '1. Change all default passwords immediately after first login'
    );
    console.log('2. Store these credentials securely');
    console.log(
      '3. Consider using environment variables for initial admin setup in production'
    );
    console.log(
      '4. The superadmin accounts can reset passwords for all other admins'
    );
  } catch (error) {
    console.error('❌ Error seeding admins:', error);
  } finally {
    process.exit(0);
  }
}

// Run the seeding function
seedAdmins();
