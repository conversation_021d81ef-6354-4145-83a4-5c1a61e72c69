import 'dotenv-flow/config';
import Mailgun from 'mailgun.js';
import fs from 'fs';
import path from 'path';
import Papa from 'papaparse';
import {
  loadEmailTemplate,
  processHtmlTemplate,
} from '../util/load-email-template';

const mailgun = new Mailgun(FormData);
const mailgunClient = mailgun.client({
  username: 'api',
  key: process.env.MAILGUN_API_KEY!,
});

function getEmailsFromCsv(csvFilename: string): string[] {
  const csvPath = path.join(process.cwd(), csvFilename);
  const csvContent = fs.readFileSync(csvPath, 'utf8');
  const { data } = Papa.parse<string[]>(csvContent, { header: false });
  // Flatten and filter valid emails
  return data
    .flat()
    .filter((email) => typeof email === 'string' && email.includes('@'));
}

async function testEmailBlast() {
  console.log('🧪 Testing sending email...');

  const htmlBody = loadEmailTemplate('email-test-template.html');
  const emails = getEmailsFromCsv('test-emails.csv');

  emails.forEach(async (toEmail: string) => {
    await mailgunClient.messages
      .create(process.env.MAILGUN_DOMAIN!, {
        from: 'ECHO360 – Next-Gen Feedback <<EMAIL>>',
        to: toEmail,
        subject: 'Please confirm you’re receiving our emails',
        html: processHtmlTemplate(htmlBody),
      })
      .catch((error) => {
        console.log(error);
      });
  });

  return;
}

testEmailBlast();
