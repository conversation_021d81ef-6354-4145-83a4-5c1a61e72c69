import dbConnect from '../lib/mongodb';
import { AdminModel } from '../lib/models/admin';

async function checkAdmins() {
  try {
    await dbConnect();
    console.log('Connected to MongoDB');

    // Count total admins
    const adminCount = await AdminModel.countDocuments();
    console.log(`Total admin accounts: ${adminCount}`);

    if (adminCount === 0) {
      console.log('No admin accounts found in the database.');
      console.log('Run the seed script to create initial admin accounts:');
      console.log('npx tsx util/seed-admins.ts');
      return;
    }

    // Get all admins (without passwords)
    const admins = await AdminModel.find({}, { password: 0 }).sort({ createdAt: -1 });
    
    console.log('\n📋 Admin Accounts:');
    console.log('='.repeat(50));
    
    admins.forEach((admin, index) => {
      console.log(`${index + 1}. ${admin.name} (${admin.email})`);
      console.log(`   Role: ${admin.role}`);
      console.log(`   Status: ${admin.isActive ? 'Active' : 'Inactive'}`);
      console.log(`   Created: ${admin.createdAt.toLocaleDateString()}`);
      console.log(`   Last Login: ${admin.lastLoginAt ? admin.lastLoginAt.toLocaleDateString() : 'Never'}`);
      console.log('-'.repeat(30));
    });

    // Check for superadmins
    const superadminCount = await AdminModel.countDocuments({ role: 'superadmin' });
    console.log(`\nSuperadmin accounts: ${superadminCount}`);
    
    if (superadminCount === 0) {
      console.log('⚠️  WARNING: No superadmin accounts found!');
      console.log('You need at least one superadmin to manage admin accounts.');
    }

  } catch (error) {
    console.error('❌ Error checking admins:', error);
  } finally {
    process.exit(0);
  }
}

// Run the check
checkAdmins(); 