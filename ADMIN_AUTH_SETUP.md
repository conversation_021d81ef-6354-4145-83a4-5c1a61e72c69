# Admin Authentication System

This document describes the newly implemented admin authentication system that protects the `/admin/dashboard` path and related admin functionality.

## Overview

The admin authentication system has been implemented with the following features:

- **Secure Login**: Email/password authentication with JWT tokens
- **Role-based Access**: Two roles: `admin` and `superadmin`
- **Password Management**: Superadmins can reset passwords for other admins
- **Session Management**: HTTP-only cookies with 8-hour expiration
- **Access Control**: All admin routes are protected by authentication middleware

## Architecture

### Database Model

A new `Admin` collection has been added with the following schema:

```typescript
interface IAdminDoc {
  email: string; // Unique email address
  password: string; // Bcrypt hashed password
  name: string; // Full name
  role: 'admin' | 'superadmin'; // Role level
  isActive: boolean; // Account status
  lastLoginAt?: Date; // Last login timestamp
  createdAt: Date; // Account creation date
  updatedAt: Date; // Last update date
}
```

### Authentication Flow

1. **Login**: <PERSON><PERSON> enters email/password at `/admin/login`
2. **Verification**: Credentials verified against database
3. **Token Generation**: JWT token created and stored as HTTP-only cookie
4. **Access Control**: All admin routes check for valid session
5. **Auto-redirect**: Unauthenticated users redirected to login

## API Endpoints

### Authentication Endpoints

- `POST /api/admin/auth/login` - Admin login
- `POST /api/admin/auth/logout` - Admin logout
- `GET /api/admin/auth/session` - Check current session

### Admin Management Endpoints (Superadmin only)

- `GET /api/admin/auth/list-admins` - List all admin accounts
- `POST /api/admin/auth/reset-password` - Reset admin password

## User Interface

### Admin Login Page

- Clean, professional login form at `/admin/login`
- Email and password fields with validation
- Password visibility toggle
- Error handling and loading states
- Automatic redirect if already logged in

### Admin Sidebar

- Displays current admin's name and role
- Shows role badge (Admin/Super Admin)
- User menu with logout option
- Admin Settings option (superadmin only)

### Admin Settings Page (Superadmin Only)

- View all admin accounts in a table
- See last login times and account status
- Reset passwords for other admins
- Cannot reset own password (security measure)

## Security Features

### Password Security

- Minimum 8 character requirement
- Bcrypt hashing with salt rounds of 12
- Passwords never stored in plain text

### Session Security

- JWT tokens with 8-hour expiration
- HTTP-only cookies (not accessible via JavaScript)
- Secure flag in production
- SameSite protection

### Access Control

- Server-side authentication checks on all admin routes
- Automatic redirects for unauthenticated access
- Role-based access for superadmin features

### Password Reset Security

- Only superadmins can reset passwords
- Cannot reset own password
- Immediate password hashing upon reset

## Role Permissions

### Admin Role

- Access to all admin dashboard features
- View and manage surveys, waves, responses
- Generate reports and exports
- Cannot manage other admin accounts

### Superadmin Role

- All admin permissions
- View all admin accounts
- Reset passwords for other admins
- Access to admin settings page

## Development Notes

### Key Files Added/Modified

```
lib/models/admin.ts                    # Admin data model
lib/api/admin-auth.ts                  # Authentication library
app/api/admin/auth/                    # Authentication API routes
app/admin/login/                       # Login page
app/admin/settings/                    # Admin management (superadmin)
app/admin/layout.tsx                   # Protected layout with auth
app/admin/_components/AdminSidebar.tsx # Updated with user info
util/seed-admins.ts                    # Database seeding script
```

### Dependencies Added

- `bcryptjs` - Password hashing
- `jsonwebtoken` - JWT token generation
- `tsx` - TypeScript execution for scripts

## Troubleshooting

### Cannot Access Admin Dashboard

1. Ensure you're logged in at `/admin/login`
2. Check that your session hasn't expired (8 hours)
3. Verify the JWT_SECRET is set in environment variables

### Seeding Script Fails

1. Ensure MongoDB is running and accessible
2. Check MONGODB_URI in environment variables
3. Verify network connectivity to database

### Password Reset Not Working

1. Ensure you're logged in as a superadmin
2. Cannot reset your own password
3. Check console for error messages

### Session Issues

1. Clear browser cookies and log in again
2. Check that JWT_SECRET is consistent
3. Verify admin account is still active

## Production Considerations

1. **Change Default Passwords**: Immediately change all seeded passwords
2. **Secure JWT Secret**: Use a strong, random JWT secret
3. **Environment Variables**: Store sensitive data in environment variables
4. **HTTPS**: Ensure cookies are only sent over HTTPS in production
5. **Database Security**: Secure MongoDB with authentication and encryption
6. **Regular Audits**: Monitor admin access and reset passwords periodically

## Future Enhancements

Potential improvements to consider:

- Password strength requirements and validation
- Account lockout after failed login attempts
- Two-factor authentication (2FA)
- Admin activity logging and audit trails
- Password change functionality for self-service
- Admin account creation via UI (superadmin only)
- Email notifications for password resets
- Session timeout warnings
